server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 包含默认的 MIME 类型配置
    include mime.types;
    default_type application/octet-stream;

    # 处理根路径请求
    location / {
        try_files $uri $uri/ /index.html;
        default_type text/html;
    }

    # 处理 JavaScript 文件
    location ~* \.(js|mjs)$ {
        default_type application/javascript;
        add_header Content-Type application/javascript;
    }
}