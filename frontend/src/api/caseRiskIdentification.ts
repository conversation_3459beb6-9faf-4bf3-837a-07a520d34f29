import axios from 'axios'
import { DataResult } from './commons'

const request = axios.create({
    baseURL: `${(import.meta as any).env.VITE_API_BASE_URL}/api`,
    timeout: 5000
})

export interface HistoryItem {
    searchContent: string
}

export interface PersonInfo {
    name: string
    idNumber: string
    brief: string
    riskLevel: 'low' | 'medium' | 'high'
    sex: string
    registeredResidence: string
    contactAddress: string
    inputText?: string
}

export interface TagItem {
    content: string
    riskLevel: 'low' | 'medium' | 'high'
    css: string
}

export interface SummaryResponse {
    tags: TagItem[]
    summary: string
}

export interface RecommendedCase {
    wenshuId: string,
    title: string,
    source: string,
    summaries: RecommendedCaseKeyValue[]
}

export interface RecommendedCaseKeyValue {
    key: string,
    value: string
}

export interface HistoricalVisitRecords {
    summary: string,
    visitRecords: HistoricalVisitRecord[]
}

export interface HistoricalVisitRecord {
    caseName: string
    caseType: string
    petitionChannel: string
    brief: string
    replyContent: string
    organizer: string
    visitTime: Date
}

export interface CaseListResponse {
    records: Array<{
        id: number;
        name: string;
        type: string;
        date: string;
        department: string;
        handler: string;
    }>;
    total: number;
    size: number;
    current: number;
}

export interface CaseListSearchParams {
    page: number;
    pageSize: number;
    searchDTO: {
        ajmc?: string;
        xyrxm?: string;
        cbbmMc?: string;
        cbjcg?: string;
        aymc?: string;
        slrqStart?: string;
        slrqEnd?: string;
    };
}

export interface GetCaseDetailRes {
    code: number;
    message: string;
    data: {
        title: string;
        paragraph: {
            key: string;
            value: string;
        }[];
    };
}

export const getHistory = async (): Promise<HistoryItem[]> => {
    try {
        const response = await request.get('/caseRiskIdentification/history').then(res => {
            return res.data
        })
        return response.data
    } catch (error) {
        console.error('Error fetching history:', error)
        return []
    }
}

export const submitIdentification = async (content: string): Promise<DataResult<PersonInfo>> => {
    const response = await request.post('/caseRiskIdentification/identification', {
        searchContent: content
    }).then(res => {
        return res.data
    })
    return response
}

export const getSummary = async (idNumber: string, name: string): Promise<SummaryResponse> => {
    try {
        const response = await request.post('/caseRiskIdentification/summary', {
            idNumber: idNumber,
            name: name
        }).then(res => {
            return res.data
        })
        return response.data
    } catch (error) {
        console.error('Error fetching summary:', error)
        return {
            tags: [],
            summary: ''
        }
    }
}

export const getRecommendedCases = async (brief: string): Promise<RecommendedCase[]> => {
    try {
        const response = await request.post('/caseRiskIdentification/recommendedCases', {
            brief: brief
        }).then(res => {
            return res.data
        })
        return response.data
    } catch (error) {
        console.error('Error fetching recommended cases:', error)
        return []
    }
}

export const getHistoricalVisitRecords = async (idNumber: string, name: string): Promise<HistoricalVisitRecords> => {
    try {
        const response = await request.post('/caseRiskIdentification/historicalVisitRecords', {
            idNumber: idNumber,
            name: name
        }).then(res => {
            return res.data
        })
        return response.data
    } catch (error) {
        console.error('Error fetching historical visit records:', error)
        return {
            summary: '',
            visitRecords: []
        }
    }
}

export const getCaseDetail = async (wenshuId: string): Promise<GetCaseDetailRes> => {
    try {
        const response = await request.get(`/caseRiskIdentification/recommendedCaseDetail?wenshuId=${wenshuId}`);
        return response.data;
    } catch (error) {
        console.error('Error fetching case detail:', error);
        return {
            code: -1,
            message: 'Failed to fetch case detail',
            data: {
                title: '',
                paragraph: []
            }
        };
    }
}

export const downloadReport = (name: string, idNumber: string): void => {
    const url = `${request.defaults.baseURL}/caseRiskIdentification/downloadReport?name=${encodeURIComponent(name)}&idNumber=${encodeURIComponent(idNumber)}`
    window.open(url)
} 