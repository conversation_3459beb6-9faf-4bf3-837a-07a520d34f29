<template>
  <div class="login-bg">
    <div class="login-title">信访案件风险识别与防控</div>
    <div class="login-card">
      <div class="login-form">
        <div class="login-form-title">登录</div>
        <el-input v-model="username" placeholder="账号" class="login-input" />
        <el-input v-model="password" placeholder="密码" type="password" class="login-input" />
        <el-button class="login-btn" type="primary" @click="handleLogin">登录</el-button>
      </div>
      <div class="login-illustration">
        <svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
          <ellipse cx="80" cy="140" rx="60" ry="15" fill="#E3F0FF"/>
          <g>
            <rect x="50" y="60" width="60" height="50" rx="10" fill="#B3D8FF"/>
            <rect x="60" y="70" width="40" height="30" rx="6" fill="#E3F0FF"/>
            <path d="M70 85l10 10 15-15" stroke="#409EFF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
          </g>
          <ellipse cx="80" cy="140" rx="60" ry="15" fill="#E3F0FF"/>
        </svg>
      </div>
    </div>
    <div class="login-copyright">@广州市人民检察院版权所有</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElInput, ElButton, ElMessage } from 'element-plus'

const username = ref('')
const password = ref('')

function handleLogin() {
  if (!username.value || !password.value) {
    ElMessage.error('请输入账号和密码')
    return
  }
  // TODO: 登录逻辑
  ElMessage.success('登录成功（示例）')
}
</script>

<style scoped>
.login-bg {
  min-height: 100vh;
  background: #54adfa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.login-title {
  color: #fff;
  font-size: 56px;
  font-weight: 600;
  margin-bottom: 60px;
  margin-top: 10px;
  letter-spacing: 2px;
}
.login-card {
  background: #fff;
  border-radius: 40px;
  box-shadow: 0 12px 48px rgba(0,0,0,0.10);
  display: flex;
  align-items: center;
  padding: 72px 100px 72px 80px;
  min-width: 900px;
  min-height: 420px;
  margin-bottom: 60px;
}
.login-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 340px;
  margin-right: 80px;
}
.login-form-title {
  color: #2196f3;
  font-size: 38px;
  font-weight: 600;
  margin-bottom: 48px;
  text-align: center;
}
.login-input {
  margin-bottom: 36px;
  height: 56px;
  font-size: 22px;
}
.login-btn {
  width: 100%;
  height: 56px;
  background: linear-gradient(90deg, #54adfa 0%, #256dff 100%);
  border: none;
  font-size: 24px;
  font-weight: 600;
}
.login-illustration {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 260px;
}
.login-illustration svg {
  width: 240px;
  height: 240px;
}
.login-copyright {
  color: #fff;
  font-size: 24px;
  text-align: center;
  margin-bottom: 48px;
}
</style> 