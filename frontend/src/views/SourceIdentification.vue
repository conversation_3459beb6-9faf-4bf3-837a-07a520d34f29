<template>
  <div class="source-identification-container">
    <!-- 侧边栏 -->
    <SideBar
        :parentKey="'source-identification'"
        :parentTitle="'信访风险源头识别与防控'"
        :items="sidebarMenuItems"
        :defaultActive="'control-measures'"
        @select="handleMenuSelect"
        @collapse-change="handleSidebarCollapse"
    />
    
    <!-- 主内容区 -->
    <div class="source-identification" :class="{ 'sidebar-shifted': !sidebarCollapsed }">
      <el-form  :model="searchForm" class="search-form" label-width="140px">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="案件名称">
              <el-input v-model="searchForm.ajmc" placeholder="请输入案件名称" clearable width="100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="嫌疑人名称">
              <el-input v-model="searchForm.xyrxm" placeholder="请输入嫌疑人名称" clearable width="100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="承办部门">
              <el-input v-model="searchForm.cbbmMc" placeholder="请输入承办部门" clearable width="100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="承办人">
              <el-input v-model="searchForm.cbjcg" placeholder="请输入承办人" clearable width="100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" style="margin-top: 12px;">
          <el-col :span="6">
            <el-form-item label="案由">
              <el-input v-model="searchForm.aymc" placeholder="请输入案由名称" clearable width="100%" />

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="受理日期">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5"></el-col>
          <el-col :span="6" style="display: flex; justify-content: flex-end; align-items: flex-end;">
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="case-list">
        <div v-for="item in pagedCases" :key="item.id" class="case-card-custom">
          <div class="case-card-top">
            <div class="case-card-header">
              <span class="case-dot"></span>
              <span class="case-title" :title="item.ajmc">{{ item.ajmc }}</span>
              <span class="case-tags">
                <span class="tag-mix">
                  <span class="tag-left" :title="item.fxsbqk">{{ item.fxsbqk }}</span>
                </span>
              </span>
            </div>
          </div>
          <!-- <div class="case-header-divider"></div> -->
          <div class="case-card-content">
            <div class="case-info">
              <div class="case-info-row"><span class="label">受理时间：</span>{{ formatDate(item.slrq) }}</div>
              <div class="case-info-row"><span class="label">承办部门：</span>{{ item.cbbmMc }}</div>
              <div class="case-info-row"><span class="label">承办人：</span>{{ item.cbjcg }}</div>
            </div>

            <div class="case-actions-custom">
              <button class="btn btn-view" @click="onView(item)">查看</button>
              <span class="btn-divider"></span>
              <button class="btn btn-push" @click="onPush(item)">推送</button>
              <span class="btn-divider"></span>
              <button class="btn btn-delete" @click="onDelete(item)">删除</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[12, 24, 36, 48]"
            :total="total"
            layout="total, sizes, prev, pager, next"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
      <AIAssistantDialog v-model="showAIDialog" />
        <!-- 悬浮AI助手图标 -->
    <AIAssistantFloat @click="handleAIAssistantClick" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCaseList, type CaseListResponse } from '@/api/caseList';
import SideBar from '@/components/SideBar.vue';
import AIAssistantDialog from '@/components/CaseRisk/AIAssistantDialog.vue'
import AIAssistantFloat from '@/components/CaseRisk/AIAssistantFloat.vue'
const router = useRouter();

// 菜单图标路径
const menuTsIcon = new URL('../assets/icon/menu-ts.svg', import.meta.url).href;

// 侧边栏状态
const sidebarCollapsed = ref(false);
const currentSideMenuItem = ref('control-measures');
const showAIDialog = ref(false)

// 侧边栏菜单项
const sidebarMenuItems = [
  {
    key: 'control-measures',
    title: '风险案件推送',
    component: 'ControlMeasures',
    icon: menuTsIcon
  }
];

const handleAIAssistantClick = () => {
  showAIDialog.value = true
}

// 处理侧边栏折叠状态变化
const handleSidebarCollapse = (collapsed: boolean) => {
  sidebarCollapsed.value = collapsed;
};

// 处理菜单项选择
const handleMenuSelect = (item: any) => {
  currentSideMenuItem.value = item.key;
  console.log('选中菜单项:', item);
  // 这里可以根据选中的菜单项加载不同的组件或数据
};

const searchForm = ref({
  ajmc: '',
  xyrxm: '',
  cbbmMc: '',
  cbjcg: '',
  aymc: '',
  dateRange: [] as string[]
});

const caseList = ref<any[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(12);

const fetchCaseList = async () => {
  const [startDate, endDate] = searchForm.value.dateRange || [];
  const response = await getCaseList({
    page: currentPage.value,
    pageSize: pageSize.value,
    searchDTO: {
      ajmc: searchForm.value.ajmc,
      xyrxm: searchForm.value.xyrxm,
      cbbmMc: searchForm.value.cbbmMc,
      cbjcg: searchForm.value.cbjcg,
      aymc: searchForm.value.aymc,
      slrqStart: startDate,
      slrqEnd: endDate
    }
  });
  caseList.value = response.data;
  total.value = response.total;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchCaseList();
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchCaseList();
};

onMounted(() => {
  fetchCaseList();
});

function onSearch() {
  currentPage.value = 1;
  fetchCaseList();
}

function onReset() {
  searchForm.value = {
    ajmc: '',
    xyrxm: '',
    cbbmMc: '',
    cbjcg: '',
    aymc: '',
    dateRange: []
  };
  currentPage.value = 1;
  fetchCaseList();
}

function onView(item: any) {
  router.push(`/case-detail/${item.bmsah}`);
}

function onPush(item: any) {
  ElMessage.success('推送成功: ' + item.bmsah);
}

function onDelete(item: any) {
  ElMessageBox.confirm(`确定要删除"${item.bmsah}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    const idx = caseList.value.findIndex((c: any) => c.id === item.id);
    if (idx !== -1) caseList.value.splice(idx, 1);
    ElMessage.success('删除成功');
  }).catch(() => {});
}

const truncateTitle = (title: string) => {
  if (!title) return '';
  return title.length > 5 ? title.slice(0, 5) + '...' : title;
};
function formatDate(val: string | null) {
  if (!val) return '';
  // 兼容 ISO 字符串和带空格的格式
  const dateStr = val.split(' ')[0].split('T')[0];
  return dateStr;
}
const pagedCases = computed(() => caseList.value);
</script>

<style scoped>
.source-identification-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 60px);
  position: relative;
}

.source-identification {
  min-height: inherit;
  padding: 10px 32px 0 32px;
  background: linear-gradient(180deg, #f7fbff 0%, #eaf3ff 100%);
  box-sizing: border-box;
  overflow-x: hidden;
  flex: 1;
  transition: margin-left 0.3s ease;
}

.sidebar-shifted {

}

/* 当侧边栏折叠时 */
.sidebar-collapsed + .source-identification {
  margin-left: 35px;
}

.main-title {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.main-title-text {
  font-size: 26px;
  font-weight: 700;
  color: #2266bb;
  letter-spacing: 1.5px;
}

.main-title-decorator {
  width: 60px;
  height: 6px;
  background: linear-gradient(90deg, #3b8cff 0%, #b3d8ff 100%);
  border-radius: 3px;
  margin-left: 18px;
}

.empty-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 500px;
  background: rgba(14, 47, 106, 0.5);
  border-radius: 6px;
  font-size: 24px;
  color: #fff;
}

.search-form {
  margin: 0 auto 10px auto;
  background: #fff;
  padding: 24px 32px 12px 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(14, 47, 106, 0.08);
  display: flex;
  flex-direction: column;
  gap: 8px 0;
  min-height: 145px;
  max-width: calc(100vw - 280px);
}

.el-form-item {
  margin-bottom: 0 !important;
  font-size: 14px;
}

.el-form-item__label {
  font-size: 14px !important;
  line-height: 36px !important;
  padding-right: 6px !important;
}

.el-input, .el-select, .el-date-editor {
  width: 100% !important;
}

.el-input__wrapper, .el-select .el-input__wrapper, .el-date-editor.el-input__wrapper {
  border-radius: 18px !important;
  border: 1.5px solid #b3d8ff !important;
  background: #f7fbff !important;
  min-height: 36px;
  font-size: 15px;
}

.el-input__inner {
  font-size: 15px;
  height: 36px !important;
  line-height: 36px !important;
}

.el-button {
  border-radius: 18px !important;
  font-size: 15px;
  padding: 0 22px;
  height: 32px;
}

.el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

.el-button:not(.el-button--primary) {
  background: #f2f3f5;
  color: #333;
  border-color: #e4e7ed;
}

.case-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px 24px;
  margin-top: 20px;
  padding-left: 150px;
  padding-right: 150px;
}

.case-card-custom {
  background: #eaf6ff;
  border: 1.5px solid #d3eaff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(14, 47, 106, 0.04);
  padding: 0;
  min-height: 210px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

.case-card-top {
  background: #eaf6ff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  padding: 18px 20px 8px 20px;
}

.case-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.case-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: linear-gradient(80deg, #39D0F8 0%, #268EF5 80%);
  margin-right: 10px;
  flex-shrink: 0;
}

.case-title {
  font-size: 17px;
  font-weight: bold;
  color: #222;
  margin-right: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
}

.case-tags {
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 28px;
  overflow: hidden;
  border-radius: 14px;
  box-shadow: none;
}

.tag-mix {
  display: flex;
  align-items: center;
  border-radius: 14px;
  overflow: hidden;
  font-size: 15px;
  font-weight: 600;
  height: 28px;
  background: #fff7e6;
  padding: 0 8px;
  width: 170px;
}

.tag-mix .tag-left {
  cursor: pointer;
  background: transparent;
  color: #ff9900;
  padding: 0 10px;
  border-radius: 0;
  height: 28px;
  display: block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-mix .tag-right {
  background: transparent;
  color: #ff9900;
  padding: 0 10px;
  border-radius: 0;
  height: 28px;
  display: flex;
  align-items: center;
}

.case-header-divider {
  height: 1.5px;
  background: #e6f0fa;
  margin: 14px 0 0 0;
  width: 100%;
}

.case-card-content {
  background: #fff;
  padding: 0 20px 0 20px;
  border-radius: 12px;
  min-height: 80px;
}

.case-info {
  padding: 16px 20px 0 20px;
  margin-bottom: 0;
  color: #666;
  font-size: 15px;
  line-height: 1.7;
  letter-spacing: 0.5px;
}
.case-info-row {
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.case-info-row .label {
  width: 80px;
  font-weight: bold;
  color: #333;
  margin-right: 4px;
}

.case-actions-divider {
  height: 1.5px;
  background: #e6f0fa;
  margin: 0;
  width: 100%;
}

.case-actions-custom {
  border-radius: 12px;
  background: #fff;
  border: 1px solid #56B3F5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  margin: 0px 12px 12px 12px;
  box-sizing: border-box;
  padding: 0;
}

.btn {
  flex: 1 1 0;
  background: none;
  border: none;
  outline: none;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  height: 100%;
  line-height: 40px;
  text-align: center;
  transition: color 0.2s;
  padding: 0;
  margin: 0;
}
.btn-view {
  color: #409eff;
}
.btn-view:hover {
  color: #1765ad;
}
.btn-push {
  color: #67c23a;
}
.btn-push:hover {
  color: #389e0d;
}
.btn-delete {
  color: #f56c6c;
}
.btn-delete:hover {
  color: #b71c1c;
}
.btn-divider {
  width: 1px;
  height: 28px;
  background: #D3EAFF;
  display: inline-block;
  margin: 0;
}

.el-pagination {
  background: #fff;
  border-radius: 18px;
  padding: 8px 24px;
  box-shadow: 0 2px 8px rgba(14, 47, 106, 0.08);
  margin: 40px auto 0 auto;
  font-size: 16px;
}

.el-pagination .el-pager li {
  border-radius: 12px !important;
  margin: 0 4px;
  min-width: 36px;
  height: 36px;
  line-height: 36px;
  font-size: 16px;
}

.el-pagination .el-pager li.is-active {
  background: #409eff !important;
  color: #fff !important;
}

.el-pagination .el-pager li:hover {
  background: #e6f0fa !important;
  color: #409eff !important;
}

.tag-divider {
  width: 1px;
  height: 16px;
  background: #ffc04d;
  display: inline-block;
  margin: 0 2px;
  align-self: center;
}

.main-content {
  flex: 1;
  max-height: calc(100vh - 210px);
  overflow-y: auto;
  min-height: 0;
}

.pagination-container {
    display: flex;
    justify-content: center;
  }
</style> 