<template>
  <div class="sync-task-manager">
    <h2>同步任务管理</h2>
    <el-form :inline="true" @submit.prevent>
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="起始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="initLoading" @click="onInitSyncTask">初始化同步任务</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="taskList" style="width: 100%; margin-top: 24px;" @selection-change="onSelectionChange">
      <el-table-column type="selection" width="55" :selectable="selectableTask" />
      <el-table-column prop="id" label="任务ID" width="100" />
      <el-table-column prop="startDate" label="起始日期" width="120" />
      <el-table-column prop="endDate" label="结束日期" width="120" />
      <el-table-column prop="totalCount" label="总记录数" width="120" />
      <el-table-column prop="successCount" label="成功记录数" width="120" />
      <el-table-column prop="beginTime" label="任务开始时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.beginTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="finishTime" label="任务完成时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.finishTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="statusType(scope.row.status)">{{ statusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button size="small" type="success" :disabled="scope.row.status === 1" :loading="startLoadingId === scope.row.id" @click="onStartTask(scope.row.id)">启动</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="table-footer-bar" style="display: flex; justify-content: space-between; align-items: center; margin-top: 24px;">
      <el-button type="primary" :disabled="selectedTasks.length === 0 || batchLoading || !selectedTasks.every(t => t.status === 0)" :loading="batchLoading" @click="onBatchStart">一键启动</el-button>
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        style="justify-content: flex-end; display: flex;"
        @current-change="onPageChange"
        @size-change="onPageSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import dayjs from 'dayjs'
axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;

const dateRange = ref<string[]>([])
const taskList = ref<any[]>([])
const initLoading = ref(false)
const startLoadingId = ref<number | null>(null)

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const selectedTasks = ref<any[]>([])
const batchLoading = ref(false)

function statusText(status: number) {
  // 0:待处理, 1:处理中, 2:已完成, 3:失败
  switch (status) {
    case 0: return '待处理'
    case 1: return '处理中'
    case 2: return '已完成'
    case 3: return '失败'
    default: return '未知'
  }
}
function statusType(status: number) {
  switch (status) {
    case 0: return 'info'
    case 1: return 'warning'
    case 2: return 'success'
    case 3: return 'danger'
    default: return 'info'
  }
}

function formatDateTime(val: string | null | undefined) {
  if (!val) return ''
  return dayjs(val).isValid() ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : ''
}

async function fetchTaskList() {
  try {
    const res = await axios.post('/api/caseRiskIdentification/listSyncTask', null, {
      params: {
        page: currentPage.value,
        pageSize: pageSize.value
      }
    })
    // 兼容后端返回格式
    if (Array.isArray(res.data.data)) {
      taskList.value = res.data.data
      total.value = res.data.total || 0
    } else {
      taskList.value = res.data
      total.value = res.data.length || 0
    }
  } catch (e) {
    ElMessage.error('获取任务列表失败')
  }
}

async function onInitSyncTask() {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择起始和结束日期')
    return
  }
  initLoading.value = true
  try {
    await axios.post('/api/caseRiskIdentification/initSyncTask', null, {
      params: {
        startDate: dateRange.value[0],
        endDate: dateRange.value[1]
      }
    })
    ElMessage.success('初始化成功')
    await fetchTaskList()
  } catch (e) {
    ElMessage.error('初始化失败')
  } finally {
    initLoading.value = false
  }
}

async function onStartTask(id: number) {
  startLoadingId.value = id
  try {
    const res = await axios.post('/api/caseRiskIdentification/startSingleSyncTask', null, { params: { id } })
    if (res.data && res.data.code === 0) {
      ElMessage.success('任务启动成功')
    } else {
      ElMessage.error(res.data?.msg || '启动失败')
    }
    await fetchTaskList()
  } catch (e) {
    ElMessage.error('启动失败')
  } finally {
    startLoadingId.value = null
  }
}

function onPageChange(page: number) {
  currentPage.value = page
  fetchTaskList()
}
function onPageSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchTaskList()
}

function onSelectionChange(val: any[]) {
  selectedTasks.value = val
}

async function onBatchStart() {
  if (!selectedTasks.value.length) return
  batchLoading.value = true
  try {
    for (const task of selectedTasks.value) {
      await axios.post('/api/caseRiskIdentification/startSingleSyncTask', null, { params: { id: task.id } })
    }
    ElMessage.success('批量启动成功')
    await fetchTaskList()
    selectedTasks.value = []
  } catch (e) {
    ElMessage.error('批量启动失败')
  } finally {
    batchLoading.value = false
  }
}

function selectableTask(row: any) {
  return row.status === 0
}

onMounted(fetchTaskList)
</script>

<style scoped>
.sync-task-manager {
  max-width: 1600px;
  min-height: 80vh;
  margin: 0 auto;
  background: #fff;
  padding: 32px 4vw;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
}
</style> 