<template>
  <div class="risk-management">
    <div class="risk-background"></div>
    <!-- 时间范围选择器 - 固定定位 -->
    <div class="fixed-date-range-container">
      <span class="date-range-label">时间范围：</span>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        size="small"
        :clearable="false"
        placement="bottom"
        popper-class="date-picker-small"
        :disabled-date="disabledDate"
        @change="handleDateChange"
      />
    </div>
    
    <div class="page-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 侧边栏 -->
      <SideBar 
        :parentKey="'risk-management'"
        :parentTitle="'信访风险源头治理'" 
        :items="sidebarMenuItems"
        :defaultActive="'data-analysis'"
        @select="handleMenuSelect"
        @collapse-change="handleSidebarCollapse"
      />
      
      <!-- 主内容区 -->
      <div class="main-container">
        <div class="main-title">
          <div class="main-title-text">
            <span class="grad-char">全</span>
            <span class="grad-char">市</span>
            <span class="grad-char">信</span>
            <span class="grad-char">访</span>
            <span class="grad-char">总</span>
            <span class="grad-char">体</span>
            <span class="grad-char">情</span>
            <span class="grad-char">况</span>
          </div>
          <div class="main-title-decorator"></div>
        </div>
        
        <div class="grid-layout">
          <!-- 左侧栏 -->
          <div class="left-column">
            <!-- 信访渠道分析 -->
            <div class="module-container">
              <div class="module-header triangle-header">信访渠道分析</div>
              <div class="module-content visit-types-content">
                <div class="visit-types">
                  <div 
                    v-for="(item, index) in visitChannelItems" 
                    :key="item.key" 
                    class="visit-type-item"
                  >
                    <span class="dot dot-1"></span>
                    <span class="dot dot-2"></span>
                    <span class="dot dot-3"></span>
                    <span class="dot dot-4"></span>
                    <div class="visit-type-circle">
                      <div class="water">
                        <div 
                          class="wave" 
                          :class="`wave-${item.key}`"
                          :style="{ bottom: `${getWaterLevel[item.key] || 30}%` }"
                        ></div>
                      </div>
                      <div class="visit-type-count">{{ item.count }}件</div>
                      <div class="visit-type-icon">
                        <img :src="getIconForType(item)" :alt="item.label" class="icon-svg" />
                      </div>
                    </div>
                    <div class="visit-type-label data-name">{{ item.label }}</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 信访风险类型分析2 -->
            <div class="module-container">
              <div class="module-header triangle-header">信访风险类型分析</div>
              <div class="module-content visit-type-chart-content">
                <VisitTypeBarChart :data="topTypesBarData" />
              </div>
            </div>
            
            <!-- 信访事项涉及罪名 -->
            <div class="module-container">
              <div class="module-header triangle-header">信访事项涉及罪名</div>
              <div class="module-content crime-types-content">
                <BarChart :data="crimeTypes.items" />
              </div>
            </div>
          </div>
          
          <!-- 中间栏 -->
          <div class="middle-column">
            <!-- 地图 -->
            <div class="module-container map-container">
              <div class="module-content">
                <div class="map-wrapper">
                  <MapChart :data="mapAreas" />
                  <div class="map-info">
                    <div class="gz-info">
                      <div class="gz-title data-name">广州市院信访量</div>
                      <div class="gz-count">{{ overview.guangzhouVisits }} <span>件</span></div>
                      <div class="gz-growth">
                        <span>同比</span>
                        <img v-if="overview.guangzhouGrowth.isIncrease" 
                             :src="shangzhangIcon" 
                             alt="上升" 
                             class="trend-icon" />
                        <img v-else
                             :src="xiajiangIcon" 
                             alt="下降" 
                             class="trend-icon" />
                        <span>{{ overview.guangzhouGrowth.value }}</span>
                      </div>
                    </div>
                    <div class="total-info">
                      <div class="total-title data-name">全市信访量</div>
                      <div class="total-count">{{ overview.totalVisits }} <span>件</span></div>
                      <div class="total-growth">
                        <span>同比</span>
                        <img v-if="overview.yearOnYearGrowth.isIncrease" 
                             :src="shangzhangIcon" 
                             alt="上升" 
                             class="trend-icon" />
                        <img v-else
                             :src="xiajiangIcon" 
                             alt="下降" 
                             class="trend-icon" />
                        <span>{{ overview.yearOnYearGrowth.value }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧栏 -->
          <div class="right-column">
            <!-- 信访分析 (带选项卡) -->
            <div class="module-container module-tabs">
              <div class="module-header">
                <div class="tab-headers">
                  <div 
                    class="tab-header triangle-header" 
                    :class="{ active: activeTab === 'field' }"
                    @click="activeTab = 'field'"
                  >
                    信访领域分析
                  </div>
                  <div 
                    class="tab-header triangle-header xfrsfx" 
                    :class="{ active: activeTab === 'person' }"
                    @click="activeTab = 'person'"
                  >
                    信访人身份分析
                  </div>
                </div>
              </div>
              <div class="module-content field-analysis-content">
                <WordCloudChart v-if="activeTab === 'field'" :data="visitFields" />
                <WordCloudChart v-else :data="personAnalysis.data" />
              </div>
            </div>
            
            <!-- 涉检情况分析(成案) -->
            <div class="module-container">
              <div class="module-header triangle-header">涉检情况分析(成案)</div>
              <div class="module-content case-analysis-content">
                <PieChart :data="caseAnalysis.data" :sidebar-collapsed="sidebarCollapsed" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import MapChart from '@/components/charts/MapChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import VisitTypeBarChart from '@/components/charts/VisitTypeBarChart.vue'
import WordCloudChart from '@/components/charts/WordCloudChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import SideBar from '@/components/SideBar.vue'
import mockData from '@/mock/data.json'
import { getAllRiskManagementData, VisitChannelItem, VisitChannels } from '@/api/riskManagement'

// SVG图标路径
const duanxinIcon = new URL('../assets/icon/duanxin.svg', import.meta.url).href
const laifangIcon = new URL('../assets/icon/laifang.svg', import.meta.url).href
const wangluoIcon = new URL('../assets/icon/wangluo.svg', import.meta.url).href
const dianhuaIcon = new URL('../assets/icon/dianhua.svg', import.meta.url).href
const shangzhangIcon = new URL('../assets/icon/shangzhang.svg', import.meta.url).href
const xiajiangIcon = new URL('../assets/icon/xiajiang.svg', import.meta.url).href

// 菜单图标路径
const menuDpIcon = new URL('../assets/icon/menu-dp.svg', import.meta.url).href
const menuSbIcon = new URL('../assets/icon/menu-sb.svg', import.meta.url).href
const menuTsIcon = new URL('../assets/icon/menu-ts.svg', import.meta.url).href

// 图标映射对象
const iconMap: Record<string, string> = {
  duanxin: duanxinIcon,
  laifang: laifangIcon,
  wangluo: wangluoIcon,
  dianhua: dianhuaIcon,
  email: duanxinIcon,      // 兼容旧key
  wechat: laifangIcon,     // 兼容旧key
  online: wangluoIcon,     // 兼容旧key
  phone: dianhuaIcon       // 兼容旧key
}

// 获取图标的函数
const getIconForType = (item: VisitChannelItem): string => {
  // 优先使用item.icon字段匹配，其次使用item.key匹配
  const iconKey = item.icon || item.key;
  return iconMap[iconKey] || '';
}

// 侧边栏状态
const sidebarCollapsed = ref(false);
const currentSideMenuItem = ref('data-analysis');

// 侧边栏菜单项
const sidebarMenuItems = [
  {
    key: 'data-analysis',
    title: '专题数据分析',
    component: 'DataAnalysis',
    icon: menuDpIcon
  }
];

// 处理侧边栏折叠状态变化
const handleSidebarCollapse = (collapsed: boolean) => {
  sidebarCollapsed.value = collapsed;
};

// 处理菜单项选择
const handleMenuSelect = (item: any) => {
  currentSideMenuItem.value = item.key;
  console.log('选中菜单项:', item);
  // 这里可以根据选中的菜单项加载不同的组件或数据
};

// 初始化时间范围选择器（当前月份到去年同月）
const now = new Date()
const currenYear = new Date()
currenYear.setDate(1) // 设置为上一年同月的第一天
currenYear.setMonth(0)
now.setMonth(4) // 设置为当前月的第一天
now.setDate(31)
const dateRange = ref<[Date, Date]>([currenYear, now])

// 禁用日期：限制时间跨度最长为一年
const disabledDate = (time: Date) => {
  if (!dateRange.value || !dateRange.value[0] || !dateRange.value[1]) {
    return false
  }
  
  const tooEarly = time.getTime() < dateRange.value[1].getTime() - 365 * 24 * 60 * 60 * 1000
  const tooLate = time.getTime() > dateRange.value[0].getTime() + 365 * 24 * 60 * 60 * 1000
  
  return tooEarly || tooLate
}

// 处理日期变化
const handleDateChange = async (val: [Date, Date]) => {
  console.log('日期范围变化:', val)
  
  // 构建请求参数
  const startDate = val[0].toISOString().split('T')[0]
  const endDate = val[1].toISOString().split('T')[0]
  
  try {
    // 调用API获取新数据
    const result = await getAllRiskManagementData(startDate, endDate)
    
    // 更新各个数据模块
    overview.value = result.overview
    visitChannels.value = result.visitChannels
    visitTopTypesData.value = {
      top1: result.visitTopTypes.top1,
      top2: result.visitTopTypes.top2,
      top3: result.visitTopTypes.top3,
      top4: result.visitTopTypes.top4,
      top5: result.visitTopTypes.top5
    }
    crimeTypes.value = result.crimeTypes
    visitFields.value = result.visitFields
    personAnalysis.value.data = result.personData
    caseAnalysis.value = result.caseAnalysis
    
    // 更新地图数据（由于mapAreas是计算属性，需要更新mockData）
    mockData.mapData = result.mapData
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 监听日期范围变化
watch(dateRange, (newVal) => {
  if (newVal && newVal[0] && newVal[1]) {
    handleDateChange(newVal)
  }
})

// 初始化数据，直接使用新的数据结构
const visitChannels = ref<VisitChannels>({
  items: [
    { key: 'email', count: "0", label: "来信", icon: 'duanxin' },
    { key: 'wechat', count: "0", label: "来访", icon: 'laifang' },
    { key: 'online', count: "0", label: "网络", icon: 'wangluo' },
    { key: 'phone', count: "0", label: "12309电话", icon: 'dianhua' }
  ]
});
const visitChannelItems = computed(() => visitChannels.value.items);

const visitTopTypesData = ref({
  top1: { count: "0", label: "重复访" },
  top2: { count: "0", label: "越级访" },
  top3: { count: "0", label: "缠闹访" },
  top4: { count: "0", label: "极端访" },
  top5: { count: "0", label: "集体访" }
});
const crimeTypes = ref(mockData.crimeTypes)
const visitFields = ref(mockData.visitFields)
const caseAnalysis = ref(mockData.caseAnalysis)
const overview = ref(mockData.overview)

// 在组件挂载时加载数据
onMounted(() => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    handleDateChange(dateRange.value)
  }
})

// 计算水位高度的函数
const getWaterLevel = computed(() => {
  const items = visitChannelItems.value;
  if (!items.length) return {};
  
  // 找出最大值
  const maxValue = Math.max(...items.map(item => parseInt(item.count)));
  
  // 计算每个项目的水位（相对于最大值的百分比）
  const result: Record<string, number> = {};
  items.forEach(item => {
    const percent = parseInt(item.count) / maxValue;
    // 水位从25%到75%，根据数据比例计算
    result[item.key] = 5 + percent * 50;
  });
  
  return result;
});

// 计算信访风险类型数据，用于条形图
const topTypesBarData = computed(() => {
  // 获取所有类型数据并转换为数组
  const dataArray = Object.entries(visitTopTypesData.value).map(([key, item]) => {
    return {
      name: item.label,
      value: parseInt(item.count) || 0
    };
  });
  
  // 过滤掉值为0的数据项
  const filteredData = dataArray.filter(item => item.value > 0);
  
  // 如果没有数据，保持原有的五种类型顺序但值为0
  if (filteredData.length === 0) {
    return [
      { name: '重复访', value: 0 },
      { name: '越级访', value: 0 },
      { name: '缠闹访', value: 0 },
      { name: '极端访', value: 0 },
      { name: '集体访', value: 0 }
    ];
  }
  
  return filteredData;
})

// 信访人身分析数据
const personAnalysis = ref({
  data: [
  { name: '涉非公企业', value: 564, x: 250, y: 250 },
    { name: '涉未成年人', value: 295, x: 420, y: 260 },
    { name: '涉港澳台', value: 207, x: 500, y: 320 },
    { name: '涉检察人员', value: 76, x: 400, y: 350 },
    { name: '涉农', value: 41, x: 350, y: 390 },
    { name: '涉律师', value: 30, x: 450, y: 410 },
    { name: '涉军', value: 18, x: 550, y: 400 },
    { name: '涉公有制企业', value: 2, x: 600, y: 330 },
    { name: '涉医', value: 37, x: 650, y: 260 },
    { name: '涉教', value: 18, x: 650, y: 380 },
    { name: '农村务工人员', value: 29, x: 650, y: 380 },
    { name: '货车司机', value: 7, x: 650, y: 380 },
    { name: '在校大学生', value: 19, x: 650, y: 380 },
    { name: '村（社区）两委人员', value: 12, x: 650, y: 380 },
    { name: '快递外卖人员', value: 4, x: 650, y: 380 },
    { name: '其他', value: 3539, x: 650, y: 380 }
  ]
})

// 当前活动标签
const activeTab = ref('field') // 'field' 表示信访领域分析, 'person' 表示信访人身分析

// 处理地图数据
const mapAreas = computed(() => {
  return mockData.mapData.areas.map((area: any) => ({
    name: area.name + '区',
    value: area.value,
    tb: area.tb // 添加tb字段
  }))
})
</script>

<style>
/* 缩小日期选择面板 - 使用全局选择器 */
.date-picker-small {
  transform: scale(0.8);
  transform-origin: top center;
}

.date-picker-small .el-picker-panel__body {
  min-width: auto !important;
}

.date-picker-small .el-date-range-picker__content {
  width: auto !important;
}

.date-picker-small .el-date-table th,
.date-picker-small .el-date-table td {
  padding: 2px !important;
  width: 34px !important;
  height: 34px !important;
}

.date-picker-small .el-date-table td .el-date-table-cell {
  height: 26px !important;
  padding: 0 !important;
  font-size: 13px !important;
}

.date-picker-small .el-picker-panel__icon-btn {
  margin: 0 2px !important;
  width: 20px !important;
  height: 20px !important;
}

.date-picker-small .el-date-picker__header-label {
  font-size: 13px !important;
  padding: 0 4px !important;
}

.date-picker-small .el-date-range-picker__header {
  margin-bottom: 2px !important;
  padding: 6px !important;
}

.date-picker-small .el-date-range-picker__body {
  padding: 5px !important;
}
</style>

<style scoped>
.risk-management {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 60px);
  position: relative;
  overflow-x: hidden; /* 添加横向溢出控制 */
  overflow-y: auto; /* 保持垂直滚动 */
}

.risk-background{
  position: fixed;
  top: 30;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000c2f;
  transition: background-color 0.3s ease;
  z-index: -100;
}

.page-container {
  display: flex;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  overflow-x: hidden; /* 添加横向溢出控制 */
}

.main-container {
  flex: 1;
  overflow-y: auto; /* 垂直方向保持滚动 */
  overflow-x: hidden; /* 横向禁止滚动 */
  transition: all 0.3s ease;
}

/* 侧边栏折叠状态下的主容器样式 */
.sidebar-collapsed .main-container {
  margin-left: 20px;
}

.risk-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/images/daping.png');
  background-size: 130%; /* 放大图片 */
  background-position: center center; /* 居中显示，凸显中间的城市 */
  background-repeat: no-repeat;
  opacity: 0.3; /* 设置透明度，确保不影响内容可见性 */
  z-index: -2; /* 确保背景图在内容之下 */
  pointer-events: none; /* 防止背景图影响鼠标事件 */
}

.main-title {
  text-align: center;
  padding: 10px 0;
  font-size: 37px;
  color: rgb(50, 243, 245);
  text-shadow: 0 0 10px rgba(50, 243, 245, 0.6);
  background: linear-gradient(to top, rgba(14, 47, 106, 0.8), rgba(0, 12, 47, 0));
  border-radius: 50%;
  margin: 0 auto 20px;
  box-shadow: 0 5px 10px rgba(0, 100, 255, 0.4);
  border: none;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  position: relative;
  padding-top: 2px;
  padding-bottom: 18px;
}

.main-title-text {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.grad-char {
  background-image: linear-gradient(to right, rgb(72, 182, 245), rgb(94, 245, 245));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: bold;
  letter-spacing: 0;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  display: inline-block;
  padding: 0 2px;
  font-size: 37px;
}

.triangle-header{
  font-family: 'Alibaba-PuHuiTi';
  position: relative;
  padding-left: 30px; /* 为三角形留出更多空间 */
  background: linear-gradient(to right, 
               rgba(63, 114, 226, 0.5) 0%, 
               rgba(32, 72, 151, 0.5) 80%,
               rgba(32, 72, 151, 0) 100%);
}

.xfrsfx{
  border-radius: 20px 20px 20px 20px !important;
  background: linear-gradient(to right, 
               rgba(63, 114, 226, 0.5) 0%, 
               rgba(32, 72, 151, 0.5) 50%, 
               transparent 50%);
}

.risk-management::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to bottom, rgba(21, 63, 101, 0.7) 0%, rgba(0, 15, 38, 0.7) 100%);
  z-index: -1;
  clip-path: ellipse(70% 100% at 50% 100%);
  -webkit-clip-path: ellipse(70% 100% at 50% 100%);
}

.grid-layout {
  display: grid;
  grid-template-columns: 1.1fr 1.3fr 1.1fr;
  gap: 15px;
  height: auto;
  min-height: calc(100vh - 120px);
}

/* 左侧栏样式 */
.visit-types {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 5px;
}

.visit-type-item {
  text-align: center;
  padding: 3px 0;
  position: relative;
  /* 添加perspective给装饰点添加3D感 */
  perspective: 100px;
}

.visit-type-circle {
  width: 95px;
  height: 95px;
  border-radius: 50%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #1E90FF;
  padding: 5px;
  position: relative;
  z-index: 1;
  /* overflow: hidden; */
  /* box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3); */
}

.data-name {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
  min-height: 30px;
  padding: 0 15px;
  border-radius: 999px;
  font-size: 15px;
  font-weight: bold;
  color: #fff;
  background: linear-gradient(
    135deg,
    #1976d2 0%,
    #1976d2 20%,
    #174a8c 100%
  );
  border: 1.5px solid rgba(25, 118, 210, 0.25);
  box-shadow:
    0 2px 8px 0 rgba(23, 76, 140, 0.18),
    0 0 0 2px rgba(25, 118, 210, 0.08) inset;
  letter-spacing: 1px;
  transition: box-shadow 0.2s;
}


/* 水面效果 */
.water {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(34, 124, 221, 0);
  overflow: hidden;
  border-radius: 50%;
  z-index: 0;
}

/* 波浪曲线 - 优化循环效果 */
.wave {
  position: absolute;
  width: 200%; /* 增加宽度使波浪更平滑 */
  height: 200%; /* 增加高度使波浪更平滑 */
  left: -50%;
  background: rgba(32, 63, 169, 0.92); 
  border-radius: 40%; /* 优化波浪形状 */
  animation: wave-animation 8s linear infinite;
  opacity: 0.88;
  z-index: 1;
  box-shadow: 0 0 15px rgba(0, 40, 120, 0.7);
}

/* 根据不同类型设置不同的波浪高度 */
.wave-email {
  bottom: 80%;
  animation-duration: 7s;
  background: linear-gradient(45deg, rgb(14, 153, 233), rgb(25, 216, 241));
}

.wave-wechat, .wave-laifang {
  animation-duration: 7s;
  background: rgba(32, 63, 169, 0.9);
}

.wave-online, .wave-wangluo {
  animation-duration: 7s;
  background: rgba(32, 63, 169, 0.9);
}

.wave-phone, .wave-dianhua {
  animation-duration: 7s;
  background: rgba(32, 63, 169, 0.9);
}

/* 动态添加其他类型的水波颜色 - 默认颜色 */
.wave {
  animation-duration: 7s;
  background: rgba(32, 63, 169, 0.9);
}

/* 波浪动画 - 优化循环 */
@keyframes wave-animation {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.visit-type-circle::before,
.visit-type-circle::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.visit-type-circle::before {
  width: 90px;
  height: 90px;
  border: 1px solid rgb(235, 239, 244);
  /* 使用conic-gradient实现平滑过渡 */
  mask: conic-gradient(from 0deg, 
                      black 0deg, 
                      black 240deg, 
                      rgba(0, 0, 0, 0.9) 250deg,
                      rgba(0, 0, 0, 0.8) 260deg,
                      rgba(0, 0, 0, 0.6) 270deg,
                      rgba(0, 0, 0, 0.4) 280deg,
                      rgba(0, 0, 0, 0.2) 290deg,
                      transparent 300deg, 
                      transparent 360deg);
  -webkit-mask: conic-gradient(from 0deg, 
                      black 0deg, 
                      black 240deg, 
                      rgba(0, 0, 0, 0.9) 250deg,
                      rgba(0, 0, 0, 0.8) 260deg,
                      rgba(0, 0, 0, 0.6) 270deg,
                      rgba(0, 0, 0, 0.4) 280deg,
                      rgba(0, 0, 0, 0.2) 290deg,
                      transparent 300deg, 
                      transparent 360deg);
}

.visit-type-circle::after {
  width: 97px;
  height: 97px;
  border: 1px solid rgb(235, 239, 244);
  /* 使用conic-gradient实现平滑过渡 */
  mask: conic-gradient(from 0deg, 
                      black 0deg, 
                      black 60deg, 
                      rgba(0, 0, 0, 0.9) 70deg,
                      rgba(0, 0, 0, 0.8) 80deg,
                      rgba(0, 0, 0, 0.6) 90deg,
                      rgba(0, 0, 0, 0.4) 100deg,
                      rgba(0, 0, 0, 0.2) 110deg,
                      transparent 120deg,
                      transparent 180deg,
                      rgba(0, 0, 0, 0.2) 190deg,
                      rgba(0, 0, 0, 0.4) 200deg,
                      rgba(0, 0, 0, 0.6) 210deg,
                      rgba(0, 0, 0, 0.8) 220deg,
                      rgba(0, 0, 0, 0.9) 230deg,
                      black 240deg,
                      black 360deg);
  -webkit-mask: conic-gradient(from 0deg, 
                      black 0deg, 
                      black 60deg, 
                      rgba(0, 0, 0, 0.9) 70deg,
                      rgba(0, 0, 0, 0.8) 80deg,
                      rgba(0, 0, 0, 0.6) 90deg,
                      rgba(0, 0, 0, 0.4) 100deg,
                      rgba(0, 0, 0, 0.2) 110deg,
                      transparent 120deg,
                      transparent 180deg,
                      rgba(0, 0, 0, 0.2) 190deg,
                      rgba(0, 0, 0, 0.4) 200deg,
                      rgba(0, 0, 0, 0.6) 210deg,
                      rgba(0, 0, 0, 0.8) 220deg,
                      rgba(0, 0, 0, 0.9) 230deg,
                      black 240deg,
                      black 360deg);
}

.visit-type-icon {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
  position: relative;
  z-index: 10;
}

.icon-svg {
  width: 35px;
  height: 35px;
  position: relative;
  z-index: 10;
}

.visit-type-count {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  position: relative;
  z-index: 10;
}

.visit-type-label {
  margin-top: 5px;
}

.visit-type-label.data-name {
  margin-top: 5px;
  font-size: 15px;
  font-family: 'Alibaba-PuHuiTi';
  padding: 2px 15px;
  display: inline-block;
  margin-bottom: 5px;
}

.visit-top-types {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 3px;
}

.visit-top-item {
  text-align: center;
  padding: 5px 0;
  color: #fff;
}

.top-number {
  font-weight: bold;
  color: #fff;
  margin-bottom: 3px;
}

.top-count {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 3px;
}

.top-label {
  color: #ffffff;
  font-size: 12px;
}

/* 中间栏样式 */
.middle-column {
  height: auto;
}

.map-container {
  height: 730px;
}

.map-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-info {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 10;
}

.gz-info {
  position: absolute;
  bottom: 140px;
  right: 70px;
  text-align: left;
  width: auto;
  padding: 10px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
}

.total-info {
  position: absolute;
  top: 40px;
  left: 80px;
  text-align: left;
  width: auto;
  padding: 10px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
}

.total-title, .gz-title {
  font-size: 20px;
  color: rgb(149, 217, 228);
  margin-bottom: 8px;
  font-weight: bold;
}

.total-count, .gz-count {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
  text-shadow: 0 0 5px #000, 0 0 10px #000;
  padding-left: 15px;
}

.total-growth, .gz-growth {
  font-size: 16px;
  color: #ffffff;
  display: flex;
  align-items: center;
  gap: 5px;
  text-shadow: 0 0 5px #000, 0 0 10px #000;
  padding-left: 15px;
}

.trend-icon {
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin: 0 2px;
}

/* 响应式布局 */
@media (max-width: 1400px) {
  .grid-layout {
    grid-template-columns: 1fr 1fr;
  }
  
  .visit-types {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .visit-top-types {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .right-column {
    grid-column: span 2;
  }
  
  .middle-column {
    display: none !important;
  }
}

@media (max-width: 1200px) {
  .grid-layout {
    grid-template-columns: 1fr;
  }
  
  .visit-types {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .visit-top-types {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .right-column {
    grid-column: span 1;
  }
  
  .middle-column {
    display: none !important;
  }
}

@media (max-width: 900px) {
  .grid-layout {
    grid-template-columns: 1fr;
  }
  
  .visit-types {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .visit-top-types {
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
  }
  
  .right-column {
    grid-column: span 1;
  }
}

/* 自定义样式覆盖 */
.module-container {
  margin-bottom: 15px;
}

.module-content {
  background: linear-gradient(to right top, rgb(25, 55, 150), rgb(2, 27, 57));
  border-radius: 4px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.grid-layout {
  height: 100%;
}

.map-container .module-content {
  background: transparent;
  box-shadow: none;
}

.crime-types-content, .case-analysis-content, .visit-types-content, .visit-type-chart-content, .field-analysis-content {
  background: linear-gradient(to right top, rgba(25, 55, 150, 0.5), rgba(2, 27, 57, 0.5));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 三角形样式已移至.triangle-header */

.triangle-header::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='16' viewBox='0 0 12 16'%3E%3Cdefs%3E%3ClinearGradient id='gradient' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' stop-color='rgb(47, 97, 200)'/%3E%3Cstop offset='100%25' stop-color='rgb(61, 162, 245)'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d='M0,0 L0,16 L12,8 Z' fill='url(%23gradient)'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

/* 选项卡样式 */
.tab-headers {
  display: flex;
  background: transparent;
}

.tab-header {
  padding: 8px 10px 8px 30px;
  cursor: pointer;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
  text-align: center;
  border-radius: 20px 20px 20px 20px;
}

.tab-header.active {
  font-weight: bold;
  color: #00fffc;
}

.tab-header:hover:not(.active) {
  color: #00fffc;
  opacity: 0.8;
}

/* 星星点点基础样式 */
.dot {
  position: absolute;
  border-radius: 50%;
  z-index: 2; /* 确保显示在圆环之上 */
  background-color: rgba(149, 225, 255, 1);
}

/* 不同大小和位置的星星点点 */
.dot-1 {
  width: 6px;
  height: 6px;
  top: 17px;
  left: 18px; /* 左上角一个大点 */
  background-color: rgb(48, 165, 241); /* 亮白色 */
}

.dot-2 {
  width: 2px;
  height: 2px;
  bottom: 62px;
  right: 21px; /* 右下角一个小点 */
  background-color: rgba(60, 153, 255, 1); /* 蓝色 */
}

.dot-3 {
  width: 4px;
  height: 4px;
  bottom: 44px;
  right: 45px; /* 右下角第二个更小的点 */
  background-color: rgb(249, 253, 253); /* 青色 */
}

.dot-4 {
  width: 2px;
  height: 2px;
  top: 25px;
  right: 17px; /* 右侧一个最小的点 */
  background-color: rgba(243, 244, 245, 0.9); /* 淡蓝色 */
}

/* 固定定位的时间范围选择器样式 */
.fixed-date-range-container {
  position: fixed;
  top: 13%;
  right: 4%;
  z-index: 9999;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-radius: 6px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.date-range-label {
  color: #00fffc;
  font-size: 16px;
  margin-right: 12px;
  font-weight: bold;
}

:deep(.el-date-editor.el-input__wrapper) {
  background: rgba(32, 63, 169, 0.8) !important;
  border: 1px solid rgb(61, 162, 245) !important;
  box-shadow: none !important;
}

:deep(.el-date-editor .el-range-separator) {
  color: #ffffff;
}

:deep(.el-date-editor .el-range-input) {
  color: #ffffff;
  background: transparent;
  font-size: 14px;
}

:deep(.el-date-editor .el-range__icon) {
  color: #00fffc;
  font-size: 16px;
}

:deep(.el-date-editor .el-range__close-icon) {
  color: #00fffc;
  font-size: 16px;
}

:deep(.el-date-editor) {
  width: 280px !important;
  transform: scale(1);
  transform-origin: right center;
}

/* 响应式样式调整 */
@media (max-width: 1200px) {
  .fixed-date-range-container {
    top: 10px;
    right: 10px;
  }
}

@media (max-width: 768px) {
  .fixed-date-range-container {
    position: fixed;
    top: auto;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    right: auto;
    width: 90%;
    max-width: 400px;
    justify-content: center;
  }
}

/* 右侧栏样式 */
.right-column {
  overflow-x: hidden; /* 防止右侧栏内容溢出 */
}
</style> 