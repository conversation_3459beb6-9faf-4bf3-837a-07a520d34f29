<template>
  <div class="header">
    <div class="system-title">
      <img :src="dianboIcon" alt="" class="system-icon" />
      信访案件风险识别与防控
    </div>
    <div class="nav-menu">
      <router-link 
        v-for="(item, index) in navItems" 
        :key="index" 
        :to="item.path" 
        class="nav-item"
        :class="{ 'active': isActiveNav(item) }"
      >
        <img v-if="item.icon" :src="item.icon" alt="" class="menu-icon" />
        {{ item.title }}
      </router-link>
    </div>
    <div class="user-info">
      <el-dropdown>
        <div class="avatar-container">
          <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
          <span class="user-name">检察官A</span>
          <el-icon><arrow-down /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>个人中心</el-dropdown-item>
            <el-dropdown-item>设置</el-dropdown-item>
            <el-dropdown-item>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'

// 导入自定义SVG图标
const dianboIcon = new URL('../assets/icon/dianbo.svg', import.meta.url).href
const dunpaiIcon = new URL('../assets/icon/dunpai.svg', import.meta.url).href
const chaxunIcon = new URL('../assets/icon/chaxun.svg', import.meta.url).href
const yunIcon = new URL('../assets/icon/yun.svg', import.meta.url).href

const router = useRouter()
const route = useRoute()

const navItems = ref([
  {
    title: '信访风险源头识别与防控',
    path: '/source-identification',
    icon: dunpaiIcon
  },
  {
    title: '案中风险识别与防控',
    path: '/risk-identification',
    icon: chaxunIcon,
    // 添加相关路径数组，包含属于此模块的所有路径
    relatedPaths: ['/risk-identification', '/visitor-portrait']
  },
  {
    title: '信访风险源头治理',
    path: '/risk-management',
    icon: yunIcon
  }
])

// 判断导航项是否处于激活状态
const isActiveNav = (item: any) => {
  // 如果有相关路径配置，检查当前路径是否在相关路径中
  if (item.relatedPaths && Array.isArray(item.relatedPaths)) {
    return item.relatedPaths.includes(route.path)
  }
  // 否则使用默认的路径匹配
  return route.path === item.path
}
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 20px;
  background: transparent;
  color: #333333;
  box-shadow: none;
  border-radius: 0 0 30px 30px;
  width: 100%;
  position: relative;
  z-index: 10;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  border-radius: 0 0 30px 30px;
  z-index: -1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.system-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: rgb(50, 112, 255);
  white-space: nowrap;
  min-width: 240px;
}

.system-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.nav-menu {
  display: flex;
  gap: 32px;
  height: 100%;
  flex: 1;
  justify-content: center;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 5px;
  text-decoration: none;
  color: #333333;
  padding: 0 23px;
  transition: all 0.3s;
  font-size: 18px;
  font-weight: 500;
  height: 70px;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.nav-item:hover {
  color: rgb(41, 60, 246);
  background-color: rgba(236, 245, 255, 0.4);
}

.nav-item.active {
  color: rgb(41, 60, 246);
  background-color: rgba(236, 245, 255, 0.8);
  border-radius: 0;
  box-shadow: none;
  height: 70px;
  margin: 0;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, rgb(117, 197, 250), rgb(41, 60, 246));
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 120px;
  justify-content: flex-end;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.user-name {
  font-size: 14px;
  margin-right: 5px;
  color: #333333;
}
</style> 