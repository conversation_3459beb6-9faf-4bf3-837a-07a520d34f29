import { getDocument, type PDFDocumentProxy } from 'pdfjs-dist'

export type Source = Parameters<typeof getDocument>[0] | PDFDocumentProxy | null

export type PasswordRequestParams = {
  callback: Function
  isWrongPassword: boolean
}

export interface VuePdfEmbedInstance {
  doc: PDFDocumentProxy | null
  download: (filename: string) => Promise<void>
  print: (dpi?: number, filename?: string, allPages?: boolean) => Promise<void>
  testAlert: (num: number) => void
}
