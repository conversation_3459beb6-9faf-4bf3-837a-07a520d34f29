<template>
    <el-dialog :model-value="modelValue" @update:model-value="emit('update:modelValue', $event)" width="1200px"
        :show-close="true" :close-on-click-modal="false" class="ai-assistant-dialog">
        <div class="ai-assistant-container">
            <!-- 左侧历史对话区 -->
            <div class="ai-assistant-sidebar">
                <div class="ai-assistant-sidebar-header">
                    <img src="@/assets/images/robot.gif" class="ai-assistant-robot" />
                    <span class="ai-assistant-title">AI问答助手</span>
                </div>
                <button class="ai-assistant-newchat" @click="createNewChat">+ 创建新对话</button>
                <div class="ai-assistant-conv-list">
                    <div v-if="isLoading" class="conv-loading">
                        <div class="loading-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="loading-text">加载历史对话中...</span>
                    </div>
                    <div v-else-if="conversations.length === 0" class="no-conversations">
                        <span>暂无历史对话</span>
                    </div>
                    <div v-else v-for="(conv, idx) in conversations" :key="conv.id || idx"
                        :class="['ai-assistant-conv-item', { active: idx === activeConvIdx, temporary: conv.isTemporary }]"
                        @click="selectConversation(idx)">
                        <div class="conv-title">
                            <img src="@/assets/icon/chat-bubble.svg" class="conv-bubble-icon" />
                            <!-- 重命名模式 -->
                            <input v-if="renamingIdx === idx" 
                                v-model="renameInput"
                                class="rename-input"
                                @keydown.enter="confirmRename(idx)"
                                @keydown.esc="cancelRename"
                                @blur="confirmRename(idx)"
                                ref="renameInputRef"
                                @click.stop="">
                            <!-- 正常显示模式 -->
                            <span v-else class="conv-title-text">{{ conv.title }}</span>
                            <span v-if="conv.isTemporary" class="temp-indicator">*</span>
                        </div>
                        <div class="conv-actions">
                            <div class="conv-time">{{ conv.time }}</div>
                            <!-- 只有非临时对话才显示操作菜单 -->
                            <div v-if="!conv.isTemporary" class="conv-menu" @click.stop="">
                                <button class="menu-trigger" @click="toggleMenu(idx)">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                                        <circle cx="3" cy="8" r="1.5"/>
                                        <circle cx="8" cy="8" r="1.5"/>
                                        <circle cx="13" cy="8" r="1.5"/>
                                    </svg>
                                </button>
                                <div v-if="activeMenuIdx === idx" class="menu-dropdown" @click.stop="">
                                    <div class="menu-item" @click="startRename(idx)">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                                            <path d="M11 2L12 3L4 11H3V10L11 2Z"/>
                                            <path d="M10.5 1.5L12.5 3.5"/>
                                        </svg>
                                        重命名
                                    </div>
                                    <div class="menu-item delete" 
                                        :class="{ loading: deletingIdx === idx }"
                                        @click="deleteConversation(idx)">
                                        <div v-if="deletingIdx === idx" class="menu-loading">
                                            <div class="menu-spinner"></div>
                                        </div>
                                        <svg v-else width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                                            <path d="M2 3H12M5 3V2C5 1.5 5.5 1 6 1H8C8.5 1 9 1.5 9 2V3M10 3V12C10 12.5 9.5 13 9 13H5C4.5 13 4 12.5 4 12V3"/>
                                        </svg>
                                        {{ deletingIdx === idx ? '删除中...' : '删除对话' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右侧聊天内容区 -->
            <div class="ai-assistant-main">
                <div class="ai-assistant-chat" ref="chatContainerRef">
                    <div v-for="(msg, idx) in activeConversation.messages" :key="idx"
                        :class="['ai-assistant-msg-wrapper', msg.role === 'user' ? 'user-wrapper' : 'bot-wrapper']">
                        <template v-if="msg.role === 'bot'">
                            <div class="bot-message-header">
                                <img src="@/assets/images/robot.gif" class="bot-avatar" />
                                <span class="bot-name">阿申</span>
                            </div>
                        </template>
                        <div v-if="msg.role === 'user' || msg.content" 
                            :class="['ai-assistant-msg', msg.role === 'user' ? 'ai-assistant-msg-user' : 'ai-assistant-msg-bot']">
                            <span v-if="msg.role === 'user'">{{ msg.content }}</span>
                            <div v-else class="markdown-content" v-html="marked(filterThinkingContent(msg.content))"></div>
                        </div>
                        <div v-else-if="msg.role === 'bot' && !msg.content" class="ai-assistant-loading">
                            <div class="loading-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ai-input-area">
                    <textarea v-model="input" 
                        :placeholder="isAIReplying ? 'AI正在回复中，请稍候...' : '请输入你的问题...'" 
                        class="ai-textarea" rows="2"
                        :disabled="isAIReplying"
                        @keydown.enter.exact.prevent="sendMessage"
                        ref="textareaRef"
                        @input="autoResizeTextarea"></textarea>
                    <div class="ai-input-actions-row">
                        <div class="ai-input-actions ai-input-actions-right">
                            <label class="ai-attach-btn">
                                <input type="file" style="display:none" @change="onFileChange" />
                                <img src="@/assets/icon/附件-上传附件.svg" class="ai-attach-icon" />
                            </label>
                            <button class="ai-send-btn" 
                                :disabled="isAIReplying || !input.trim()"
                                :class="{ disabled: isAIReplying || !input.trim() }"
                                @click="sendMessage">
                                <div v-if="isAIReplying" class="ai-send-loading">
                                    <div class="loading-spinner"></div>
                                </div>
                                <img v-else src="@/assets/icon/send-icon.svg" class="ai-send-icon" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineEmits, nextTick, onMounted, onUnmounted } from 'vue'
import { marked } from 'marked'
import { ElMessageBox } from 'element-plus'
import { listHistoryChats, sendChatMessage, createNewChatSession, updateChatTitle, deleteChatSession, type HistoryChats, type ChatStreamCallbacks } from '@/api/knowledgeBase'

const props = defineProps({
    modelValue: Boolean
})
const emit = defineEmits(['update:modelValue'])

// Configure marked options
marked.setOptions({
    breaks: true, // Enable line breaks
    gfm: true, // Enable GitHub Flavored Markdown
})

const input = ref('')
const conversations = ref<Array<{
    id: string | number | undefined,
    title: string,
    time: string,
    messages: Array<{ role: string, content: string }>,
    isTemporary?: boolean // 标记是否为临时对话（未保存到历史记录）
}>>([])
const activeConvIdx = ref(0)
const isConversationSwitching = ref(false) // 标记是否正在切换对话
const activeConversation = computed(() => {
    if (conversations.value.length === 0) {
        return { 
            id: undefined as string | number | undefined,
            title: '新对话',
            time: new Date().toLocaleString(),
            messages: [] as Array<{ role: string, content: string }>,
            isTemporary: true
        }
    }
    return conversations.value[activeConvIdx.value]
})
const isLoading = ref(false)
const isAIReplying = ref(false) // 跟踪AI是否正在回复
const activeMenuIdx = ref<number | null>(null) // 当前打开的菜单索引
const renamingIdx = ref<number | null>(null) // 当前正在重命名的对话索引
const renameInput = ref('') // 重命名输入框的值
const deletingIdx = ref<number | null>(null) // 正在删除的对话索引
const updatingIdx = ref<number | null>(null) // 正在更新的对话索引
const currentSSE = ref<any>(null) // 当前的SSE连接实例

const textareaRef = ref<HTMLTextAreaElement | null>(null)
const chatContainerRef = ref<HTMLElement | null>(null)

// 获取历史对话列表
const fetchHistoryChats = async () => {
    try {
        isLoading.value = true
        const response = await listHistoryChats(1, 20) // 获取第一页，20条记录
        
        if (response.data && Array.isArray(response.data)) {
            // 保存当前激活的对话ID，用于刷新后重新定位
            const currentActiveId = conversations.value[activeConvIdx.value]?.id
            
            // 更新历史对话列表
            const historyConversations = response.data.map((chat: HistoryChats) => ({
                id: chat.id,
                title: chat.name || '未命名对话',
                time: new Date(chat.create_time).toLocaleString(),
                messages: chat.messages || []
                // 历史记录不是临时的，所以不设置isTemporary
            }))
            
            // 合并临时对话和历史对话
            const tempConversations = conversations.value.filter(conv => conv.isTemporary)
            conversations.value = [...tempConversations, ...historyConversations]
            
            // 尝试恢复之前选中的对话
            if (currentActiveId) {
                const foundIndex = conversations.value.findIndex(conv => conv.id === currentActiveId)
                if (foundIndex !== -1) {
                    activeConvIdx.value = foundIndex
                } else {
                    // 如果找不到之前的对话，选择第一个非临时对话
                    const firstNonTempIndex = conversations.value.findIndex(conv => !conv.isTemporary)
                    activeConvIdx.value = firstNonTempIndex !== -1 ? firstNonTempIndex : 0
                }
            } else if (conversations.value.length > 0) {
                // 如果之前没有选中对话，优先选择第一个非临时对话
                const firstNonTempIndex = conversations.value.findIndex(conv => !conv.isTemporary)
                activeConvIdx.value = firstNonTempIndex !== -1 ? firstNonTempIndex : 0
            }
        }
    } catch (error) {
        console.error('获取历史对话失败:', error)
        // 如果接口失败，确保至少有一个可用的对话
        if (conversations.value.length === 0) {
            conversations.value = [{
                id: undefined,
                title: '新对话',
                time: new Date().toLocaleString(),
                messages: [],
                isTemporary: true
            }]
            activeConvIdx.value = 0
        }
    } finally {
        isLoading.value = false
    }
}

// 组件挂载时不再自动获取历史对话，改为每次打开对话框时刷新

// 组件卸载时清理资源（已合并到下面的onUnmounted中）

// 过滤AI思考过程的函数
function filterThinkingContent(content: string): string {
    if (!content) return content
    
    let filteredContent = content
    
    // 移除完整的<think>...</think>标签及其内容（支持多行）
    filteredContent = filteredContent.replace(/<think>[\s\S]*?<\/think>/gi, '')
    
    // 移除未闭合的<think>标签到字符串末尾的内容
    filteredContent = filteredContent.replace(/<think>[\s\S]*$/gi, '')
    
    // 移除单独的</think>闭合标签（防止不匹配的情况）
    filteredContent = filteredContent.replace(/<\/think>/gi, '')
    
    // 清理多余的空白行
    filteredContent = filteredContent.replace(/\n\s*\n\s*\n/g, '\n\n')
    
    return filteredContent.trim()
}

function autoResizeTextarea() {
    nextTick(() => {
        const ta = textareaRef.value
        if (ta) {
            ta.style.height = 'auto'
            ta.style.overflowY = 'hidden'
            ta.style.height = Math.min(ta.scrollHeight, 6 * 28) + 'px' // 6行高度
            if (ta.scrollHeight > 6 * 28) {
                ta.style.overflowY = 'auto'
            }
        }
    })
}

function scrollToBottom(smooth: boolean = true) {
    nextTick(() => {
        if (chatContainerRef.value) {
            if (smooth) {
                chatContainerRef.value.scrollTo({
                    top: chatContainerRef.value.scrollHeight,
                    behavior: 'smooth'
                })
            } else {
                chatContainerRef.value.scrollTop = chatContainerRef.value.scrollHeight
            }
        }
    })
}

async function sendMessage() {
    if (!input.value.trim() || isAIReplying.value) return
    
    // 如果没有对话，先创建一个新对话
    if (conversations.value.length === 0) {
        createNewChat()
    }
    
    // 设置AI正在回复状态
    isAIReplying.value = true
    
    const userMessage = input.value
    
    // 如果是临时对话，使用消息前16个字作为会话标题
    if (activeConversation.value.isTemporary) {
        const sessionName = userMessage.length > 16 ? userMessage.substring(0, 16) + '...' : userMessage
        conversations.value[activeConvIdx.value].title = sessionName
    }
    
    // Add user message
    activeConversation.value.messages.push({ role: 'user', content: userMessage })
    input.value = ''
    
    // Reset textarea height
    nextTick(() => {
        const ta = textareaRef.value
        if (ta) {
            ta.style.height = '44px'
            ta.style.overflowY = 'hidden'
        }
    })

    // Add initial bot message with empty content to show loading
    const botMessageIndex = activeConversation.value.messages.length
    activeConversation.value.messages.push({ role: 'bot', content: '' })
    
    // Scroll to bottom after adding messages
    scrollToBottom()

    try {
        // 获取或创建sessionId
        let sessionId = activeConversation.value.id?.toString()
        
        // 如果当前对话没有id，则创建新的会话
        if (!sessionId) {
            console.log('当前对话没有ID，正在创建新会话...')
            const sessionName = activeConversation.value.title
            const newSession = (await createNewChatSession(sessionName)).data
            sessionId = newSession.id

            // 更新当前对话的ID，并标记为非临时对话
            conversations.value[activeConvIdx.value].id = newSession.id
            conversations.value[activeConvIdx.value].isTemporary = false
            conversations.value[activeConvIdx.value].time = new Date(newSession.create_time).toLocaleString()
            console.log('新会话已创建，sessionId:', sessionId, 'sessionName:', sessionName)
        }

        const callbacks: ChatStreamCallbacks = {
            onMessage: (content: string) => {
                // Append new content to the bot's message
                activeConversation.value.messages[botMessageIndex].content += content
                
                // 实时过滤显示内容（用于UI显示，但保留原始内容）
                // 注意：这里我们不修改原始content，而是在显示时过滤
                // Scroll to bottom after each message update
                scrollToBottom()
            },
            onError: (error: Error) => {
                console.error('Chat Error:', error)
                // Add error message to the conversation
                activeConversation.value.messages[botMessageIndex].content = '抱歉，发生了一些错误，请稍后重试。'
                scrollToBottom()
                // 清理连接引用和状态
                currentSSE.value = null
                isAIReplying.value = false
            },
            onComplete: () => {
                console.log('Chat completed')
                // 清理连接引用和状态
                currentSSE.value = null
                isAIReplying.value = false
            }
        }

        // 如果有正在进行的连接，先关闭它
        if (currentSSE.value) {
            currentSSE.value.close()
        }

        // 使用新的API函数发送消息，传入sessionId
        currentSSE.value = sendChatMessage(sessionId, userMessage, callbacks)
    } catch (error) {
        console.error('Fetch Error:', error)
        activeConversation.value.messages[botMessageIndex].content = '抱歉，发生了一些错误，请稍后重试。'
        scrollToBottom()
        // 清理AI回复状态
        isAIReplying.value = false
    }
}

function createNewChat() {
    const now = new Date()
    const title = '新对话'
    
    // 创建临时对话，不会保存到服务器历史记录，只在首次发送消息时才创建真实会话
    conversations.value.unshift({
        id: undefined, // 临时对话没有服务器ID
        title,
        time: now.toLocaleString(),
        messages: [],
        isTemporary: true // 标记为临时对话
    })
    activeConvIdx.value = 0
}

function selectConversation(idx: number) {
    isConversationSwitching.value = true
    activeConvIdx.value = idx
    // 关闭任何打开的菜单
    activeMenuIdx.value = null
    // 切换对话时直接跳转到底部，不使用平滑滚动
    scrollToBottom(false)
    // 延迟重置切换标记，确保watch不会再次触发滚动
    nextTick(() => {
        isConversationSwitching.value = false
    })
}

// 切换菜单显示/隐藏
function toggleMenu(idx: number) {
    if (activeMenuIdx.value === idx) {
        activeMenuIdx.value = null
    } else {
        activeMenuIdx.value = idx
    }
}

// 开始重命名对话
function startRename(idx: number) {
    renamingIdx.value = idx
    renameInput.value = conversations.value[idx].title
    activeMenuIdx.value = null
    
    // 下一帧聚焦到输入框
    nextTick(() => {
        const input = document.querySelector('.rename-input') as HTMLInputElement
        if (input) {
            input.focus()
            input.select()
        }
    })
}

// 确认重命名
async function confirmRename(idx: number) {
    if (renameInput.value.trim()) {
        const newSessionName = renameInput.value.trim()
        const conversation = conversations.value[idx]
        
        // 先更新UI
        const oldTitle = conversation.title
        conversation.title = newSessionName
        
        // 调用API更新服务器端的对话标题
        if (conversation.id) {
            try {
                updatingIdx.value = idx // 设置更新状态
                const success = await updateChatTitle(conversation.id.toString(), newSessionName)
                if (!success) {
                    // 如果API调用失败，恢复原标题
                    conversation.title = oldTitle
                    alert('重命名失败，请稍后重试')
                }
            } catch (error) {
                // 如果出现异常，恢复原标题
                conversation.title = oldTitle
                console.error('重命名对话失败:', error)
                alert('重命名失败，请稍后重试')
            } finally {
                updatingIdx.value = null
            }
        }
    }
    renamingIdx.value = null
    renameInput.value = ''
}

// 取消重命名
function cancelRename() {
    renamingIdx.value = null
    renameInput.value = ''
}

// 删除对话
async function deleteConversation(idx: number) {
    try {
        await ElMessageBox.confirm(
            '确定要删除这个对话吗？此操作不可撤销。',
            '删除确认',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                customClass: 'delete-conversation-dialog'
            }
        )
        
        const conversation = conversations.value[idx]
        
        // 如果有会话ID，先调用API删除服务器端的对话
        let apiSuccess = true
        if (conversation.id) {
            try {
                deletingIdx.value = idx // 设置删除状态
                apiSuccess = await deleteChatSession(conversation.id.toString())
                if (!apiSuccess) {
                    ElMessageBox.alert('删除失败，请稍后重试', '操作失败', {
                        type: 'error'
                    })
                    activeMenuIdx.value = null
                    deletingIdx.value = null
                    return
                }
            } catch (error) {
                console.error('删除对话失败:', error)
                ElMessageBox.alert('删除失败，请稍后重试', '操作失败', {
                    type: 'error'
                })
                activeMenuIdx.value = null
                deletingIdx.value = null
                return
            } finally {
                deletingIdx.value = null
            }
        }
        
        // API调用成功后，从列表中移除对话
        conversations.value.splice(idx, 1)
        
        // 调整活动对话索引
        if (activeConvIdx.value === idx) {
            // 如果删除的是当前活动对话，选择下一个或上一个
            if (conversations.value.length > 0) {
                activeConvIdx.value = Math.min(idx, conversations.value.length - 1)
            } else {
                // 如果没有对话了，创建一个新的临时对话
                createNewChat()
            }
        } else if (activeConvIdx.value > idx) {
            // 如果删除的对话在当前活动对话之前，调整索引
            activeConvIdx.value--
        }
        
        activeMenuIdx.value = null
    } catch {
        // 用户取消删除，关闭菜单
        activeMenuIdx.value = null
    }
}

function onFileChange(e: Event) {
    // 这里可以处理附件上传逻辑
    // const file = (e.target as HTMLInputElement).files?.[0]
}

// Watch for conversation changes to scroll to bottom
watch(() => activeConversation.value?.messages, () => {
    // 如果不是切换对话引起的变化，则使用平滑滚动
    if (!isConversationSwitching.value) {
        scrollToBottom(false)
    }
}, { deep: true })

watch(() => props.modelValue, v => {
    if (!v) {
        input.value = ''
        // 对话框关闭时清理SSE连接和状态
        if (currentSSE.value) {
            currentSSE.value.close()
            currentSSE.value = null
        }
        isAIReplying.value = false
    } else {
        // 每次对话框打开时都刷新历史记录
        fetchHistoryChats()
    }
})

// 点击外部关闭菜单
function handleClickOutside(event: Event) {
    const target = event.target as HTMLElement
    if (!target.closest('.conv-menu')) {
        activeMenuIdx.value = null
    }
}

// 组件挂载时添加全局点击监听
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除监听器
onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    if (currentSSE.value) {
        currentSSE.value.close()
        currentSSE.value = null
    }
    // 清理AI回复状态
    isAIReplying.value = false
})
</script>

<style scoped>
.ai-assistant-dialog .el-dialog {
    margin-top: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
}

.ai-assistant-dialog .el-dialog__body {
    padding: 0;
}

.ai-assistant-container {
    display: flex;
    height: 800px;
}

.ai-assistant-sidebar {
    width: 260px;
    background: #f7fbff;
    border-right: 1px solid #e3f2fd;
    display: flex;
    flex-direction: column;
    align-items: stretch;
}

.ai-assistant-sidebar-header {
    display: flex;
    align-items: center;
    padding: 24px 0 12px 24px;
}

.ai-assistant-robot {
    width: 38px;
    height: 38px;
    margin-right: 12px;
}

.ai-assistant-title {
    font-size: 20px;
    font-weight: bold;
    color: #222e50;
}

.ai-assistant-newchat {
    margin: 0 18px 12px 18px;
    padding: 10px 0;
    background: #5289e0;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.ai-assistant-newchat:hover {
    background: #347BE3;
}

.ai-assistant-conv-list {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 18px;
}

.ai-assistant-conv-item {
    padding: 14px 18px 10px 16px;
    cursor: pointer;
    color: #4C5674;
    border-left: none;
    transition: background 0.2s, border-color 0.2s;
    margin-bottom: 2px;
    background: transparent;
}

.ai-assistant-conv-item.active {
    background: #e3f2fd;
    color: #5289e0;
}

.ai-assistant-conv-item.temporary {
    background: #fff9e6;
    opacity: 0.8;
}

.ai-assistant-conv-item.temporary.active {
    background: #fff3cd;
    color: #856404;
}

.conv-title {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 2px;
    display: flex;
    align-items: center;
}

.conv-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.conv-time {
    font-size: 12px;
    color: #888;
    margin-left: 25px;
    /* 18px图标+7px间距 */
}

.ai-assistant-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
}

.ai-assistant-chat {
    flex: 1;
    padding: 32px 36px 0 36px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 18px;
    /* scroll-behavior: smooth; 移除全局平滑滚动，改为JavaScript控制 */
}

.ai-assistant-msg-wrapper {
    display: flex;
    flex-direction: column;
    max-width: 80%;
}

.bot-wrapper {
    align-self: flex-start;
}

.user-wrapper {
    align-self: flex-end;
}

.bot-message-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    padding-left: 4px;
}

.bot-avatar {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.bot-name {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.ai-assistant-msg {
    padding: 14px 18px;
    border-radius: 16px;
    font-size: 16px;
    line-height: 1.7;
    word-break: break-all;
}

.ai-assistant-msg-user {
    background: #e3f2fd;
    color: #1976d2;
}

.ai-assistant-msg-bot {
    background: #fff;
    color: #333;
    border: 1px solid #e5e6eb;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
}

.ai-assistant-input {
    padding: 10px 20px;
    border-top: 1px solid #e3f2fd;
    display: flex;
    align-items: flex-end;
}

.ai-input-area {
    margin-left: 7px;
    margin-top: 20px;
    position: relative;
    width: 100%;
    background: #f5f6fa;
    border-radius: 28px;
    padding: 18px 24px 10px 24px;
    min-height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.ai-textarea {
    width: 100%;
    min-height: 44px;
    max-height: 168px; /* 6行左右 */
    resize: none;
    border: none;
    background: transparent;
    font-size: 17px;
    color: #222;
    outline: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    line-height: 1.7;
    font-family: inherit;
    overflow-y: hidden;
    transition: opacity 0.2s;
}

.ai-textarea:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    color: #999;
}

.ai-input-actions-row {
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

.ai-input-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    position: static;
}

.ai-attach-btn {
    background: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(33, 150, 243, 0.06);
    transition: background 0.2s;
    padding: 0;
}

.ai-attach-btn:hover {
    background: #e3f2fd;
}

.ai-attach-icon {
    width: 22px;
    height: 22px;
    display: block;
}

.ai-send-btn {
    background: #e3e8ef;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(33, 150, 243, 0.06);
    transition: background 0.2s;
    padding: 0;
}

.ai-send-btn:hover:not(:disabled) {
    background: #d2e3fc;
}

.ai-send-btn.disabled,
.ai-send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #e8e8e8;
}

.ai-send-loading {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e8e8e8;
    border-top: 2px solid #5289e0;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.ai-send-icon {
    width: 22px;
    height: 22px;
    display: block;
}

.conv-bubble-icon {
    width: 18px;
    height: 18px;
    margin-right: 7px;
    vertical-align: middle;
}

.dialog-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0;
    margin: 0;
}

.close-button {
    padding: 8px;
    border: none;
    background: transparent;
    cursor: pointer;
}

.close-button:hover {
    background: #f5f6fa;
    border-radius: 4px;
}

.close-button img {
    width: 16px;
    height: 16px;
}

.ai-assistant-loading {
    padding: 14px 18px;
    border-radius: 16px;
    background: #fff;
    border: 1px solid #e5e6eb;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
}

.loading-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: #5289e0;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes bounce {
    0%, 80%, 100% { 
        transform: scale(0);
    }
    40% { 
        transform: scale(1.0);
    }
}

.markdown-content {
    line-height: 1.7;
}

.markdown-content :deep(p) {
    margin: 0.5em 0;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
    margin: 1em 0 0.5em;
    font-weight: 600;
    line-height: 1.25;
}

.markdown-content :deep(h1) { font-size: 1.5em; }
.markdown-content :deep(h2) { font-size: 1.3em; }
.markdown-content :deep(h3) { font-size: 1.2em; }
.markdown-content :deep(h4) { font-size: 1.1em; }
.markdown-content :deep(h5) { font-size: 1em; }
.markdown-content :deep(h6) { font-size: 0.9em; }

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
    padding-left: 1.5em;
    margin: 0.5em 0;
}

.markdown-content :deep(li) {
    margin: 0.25em 0;
}

.markdown-content :deep(code) {
    background: #f5f7fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

.markdown-content :deep(pre) {
    background: #f5f7fa;
    padding: 1em;
    border-radius: 6px;
    overflow-x: auto;
    margin: 0.5em 0;
}

.markdown-content :deep(pre code) {
    background: none;
    padding: 0;
    border-radius: 0;
}

.markdown-content :deep(blockquote) {
    border-left: 4px solid #e3f2fd;
    margin: 0.5em 0;
    padding: 0.5em 0 0.5em 1em;
    color: #666;
}

.markdown-content :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 0.5em 0;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
    border: 1px solid #e3f2fd;
    padding: 0.5em;
    text-align: left;
}

.markdown-content :deep(th) {
    background: #f5f7fa;
}

.markdown-content :deep(a) {
    color: #5289e0;
    text-decoration: none;
}

.markdown-content :deep(a:hover) {
    text-decoration: underline;
}

.markdown-content :deep(img) {
    max-width: 100%;
    height: auto;
}

.conv-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    color: #666;
}

.conv-loading .loading-dots {
    margin-bottom: 8px;
}

.loading-text {
    font-size: 14px;
    color: #888;
}

.no-conversations {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #888;
    font-size: 14px;
}

.temp-indicator {
    background-color: #ffd700;
    color: #000;
    padding: 2px 4px;
    border-radius: 4px;
    font-size: 0.8em;
    margin-left: 4px;
    font-weight: bold;
}

.conv-menu {
    position: relative;
    display: flex;
    align-items: center;
}

.menu-trigger {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #888;
    opacity: 0;
    transition: opacity 0.2s;
    border-radius: 3px;
}

.menu-trigger:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #666;
}

.ai-assistant-conv-item:hover .menu-trigger {
    opacity: 1;
}

.menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: #fff;
    border: 1px solid #e3f2fd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    z-index: 1000;
    min-width: 120px;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    color: #4C5674;
    transition: background 0.2s;
    font-size: 14px;
    white-space: nowrap;
}

.menu-item:hover {
    background: #f5f6fa;
}

.menu-item.delete {
    color: #e74c3c;
}

.menu-item.delete:hover {
    background: #fdf2f2;
}

.menu-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

.menu-item svg {
    width: 14px;
    height: 14px;
    margin-right: 8px;
}

.menu-loading {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.menu-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid #e8e8e8;
    border-top: 2px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.rename-input {
    flex: 1;
    background: #fff;
    border: 1px solid #5289e0;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 14px;
    color: #333;
    outline: none;
    box-shadow: 0 0 3px rgba(82, 137, 224, 0.3);
}

.conv-title-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 删除确认弹窗样式 */
:global(.delete-conversation-dialog) {
    border-radius: 12px;
}

:global(.delete-conversation-dialog .el-message-box__header) {
    padding: 24px 24px 12px;
}

:global(.delete-conversation-dialog .el-message-box__title) {
    color: #333;
    font-weight: 600;
    font-size: 16px;
}

:global(.delete-conversation-dialog .el-message-box__content) {
    padding: 0 24px 24px;
}

:global(.delete-conversation-dialog .el-message-box__message) {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
}

:global(.delete-conversation-dialog .el-message-box__btns) {
    padding: 16px 24px 24px;
    text-align: right;
}

:global(.delete-conversation-dialog .el-button--primary) {
    background-color: #e74c3c;
    border-color: #e74c3c;
    padding: 8px 20px;
    border-radius: 6px;
    font-weight: 500;
}

:global(.delete-conversation-dialog .el-button--primary:hover) {
    background-color: #c0392b;
    border-color: #c0392b;
}

:global(.delete-conversation-dialog .el-button--default) {
    padding: 8px 20px;
    border-radius: 6px;
    margin-right: 12px;
    color: #666;
    border-color: #ddd;
}

:global(.delete-conversation-dialog .el-button--default:hover) {
    color: #5289e0;
    border-color: #5289e0;
}
</style>