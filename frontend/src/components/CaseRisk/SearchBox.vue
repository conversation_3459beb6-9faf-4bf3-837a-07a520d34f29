<template>
  <div class="input-section">
    <div class="input-box-border">
      <div class="input-box">
        <input v-model="localInputText" type="text" :placeholder="placeholder" @keydown.enter="onSubmit" />
        <button @click="onSubmit">
          <img src="@/assets/icon/放大镜.svg" width="24" height="24" alt="分析" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: string
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'submit'): void
}>()

const localInputText = ref(props.modelValue)

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  localInputText.value = newValue
})

// Watch for local changes
watch(localInputText, (newValue) => {
  emit('update:modelValue', newValue)
})

const onSubmit = () => {
  emit('submit')
}
</script>

<style scoped>
.input-section {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 10px 0;
}

.input-box-border {
  border: 1.5px solid transparent;
  border-radius: 30px;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to right, rgb(74 177 244), rgb(47 63 245));
}

.input-box {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.08);
  padding: 0 10px;
  width: 900px;
  max-width: 90vw;
  height: 60px;
  /* border: 1px solid rgb(71 128 244); */
}

.input-box input {
  border: none;
  outline: none;
  font-size: 18px;
  flex: 1;
  padding: 16px 12px;
  background: transparent;
}

.input-box button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0 8px;
  display: flex;
  align-items: center;
}
</style>