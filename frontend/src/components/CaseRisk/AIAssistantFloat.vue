<template>
  <div
    class="ai-assistant-float"
    :class="{ 'dragging': isDragging }"
    :style="{ right: dragPosition.x + 'px', top: dragPosition.y + '%', transform: 'translateY(-50%)', position: 'fixed' }"
    @click="handleClick"
    @mousedown="handleMouseDown"
  >
    <img src="@/assets/images/robot.gif" width="60" height="60" alt="AI助手" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits(['click'])

// 拖拽相关状态
const isDragging = ref(false)
const dragPosition = ref({ x: 36, y: 50 }) // 初始位置：right: 36px, top: 50%
const dragStartTime = ref(0)
const longPressThreshold = 200 // 长按阈值200ms
const dragStartPos = ref({ x: 0, y: 0 })
const dragOffset = ref({ x: 0, y: 0 }) // 记录鼠标点击位置与机器人左上角的偏移量
const isClick = ref(true) // 用于判断是否为点击事件

// 拖拽相关方法
const handleMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  dragStartTime.value = Date.now()
  isClick.value = true // 初始设置为点击事件
  
  // 计算鼠标点击位置与机器人左上角的偏移量
  const rect = document.documentElement.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const iconWidth = 60
  const iconHeight = 60
  
  // 计算机器人左上角的位置
  const robotLeft = windowWidth - dragPosition.value.x - iconWidth
  const robotTop = (dragPosition.value.y / 100) * window.innerHeight - iconHeight / 2
  
  // 计算偏移量
  dragOffset.value = {
    x: event.clientX - robotLeft,
    y: event.clientY - robotTop
  }
  
  dragStartPos.value = { x: event.clientX, y: event.clientY }
  
  // 设置长按定时器
  const longPressTimer = setTimeout(() => {
    if (Date.now() - dragStartTime.value >= longPressThreshold) {
      isDragging.value = true
      isClick.value = false // 进入拖拽状态，不是点击
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }
  }, longPressThreshold)

  // 添加鼠标抬起事件监听，用于取消长按定时器
  const mouseUpHandler = () => {
    clearTimeout(longPressTimer)
    document.removeEventListener('mouseup', mouseUpHandler)
  }
  document.addEventListener('mouseup', mouseUpHandler)
}

const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  // 如果移动距离超过阈值，则不是点击
  const moveDistance = Math.sqrt(
    Math.pow(event.clientX - dragStartPos.value.x, 2) + 
    Math.pow(event.clientY - dragStartPos.value.y, 2)
  )
  if (moveDistance > 5) { // 5像素的移动阈值
    isClick.value = false
  }
  
  const rect = document.documentElement.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const iconWidth = 60
  const iconHeight = 60
  const topBarHeight = 70 // 顶部导航栏高度
  
  // 计算机器人左上角的新位置
  const newLeft = event.clientX - dragOffset.value.x
  const newTop = event.clientY - dragOffset.value.y
  
  // 转换为right和top百分比
  const newX = windowWidth - newLeft - iconWidth
  const newY = ((newTop + iconHeight / 2) / windowHeight) * 100
  
  // 限制拖拽范围，确保图标完整显示且不被顶部导航栏遮挡
  const minX = 0 // 最小距离右边距离
  const maxX = windowWidth - iconWidth // 最大距离右边距离（减去图标宽度）
  const minY = ((topBarHeight + iconHeight / 2) / windowHeight) * 100 // 最小top百分比（顶部栏高度+图标高度的一半）
  const maxY = ((windowHeight - iconHeight / 2) / windowHeight) * 100 // 最大top百分比
  
  dragPosition.value = {
    x: Math.max(minX, Math.min(maxX, newX)),
    y: Math.max(minY, Math.min(maxY, newY))
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

const handleClick = () => {
  // 只有在非拖拽状态且是点击事件时才触发点击事件
  if (!isDragging.value && isClick.value) {
    emit('click')
  }
}
</script>

<style scoped>
.ai-assistant-float {
  cursor: pointer;
  z-index: 1000;
  transition: transform 0.2s;
  user-select: none;
}

.ai-assistant-float:active {
  transform: scale(0.95);
}

.ai-assistant-float.dragging {
  transform: scale(1.1) translateY(-50%);
  cursor: grabbing;
  opacity: 0.8;
  transition: none;
}
</style> 