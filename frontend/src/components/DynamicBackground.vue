<template>
  <div class="dynamic-background" :style="backgroundStyle"></div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const bgColor = ref('#000c2f') // 默认深蓝色背景

// 根据路由路径设置背景色
const updateBackgroundColor = () => {
  switch (route.path) {
    case '/risk-management':
      // 深蓝色背景
      bgColor.value = '#f7fbff'
      break
    case '/risk-identification':
      // 浅蓝背景 - 与CaseRisk.vue的背景匹配
      bgColor.value = '#f7fbff'
      break
    case '/source-identification':
    case '/risk-identification':
    case '/visitor-portrait':
    default:
      // 深蓝色背景 - 保持一致性
      bgColor.value = '#f7fbff'
  }
}

// 计算样式对象
const backgroundStyle = computed(() => ({
  backgroundColor: bgColor.value
}))

// 监听路由变化
watch(() => route.path, () => {
  updateBackgroundColor()
}, { immediate: true })
</script>

<style scoped>
.dynamic-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -100; /* 确保在所有内容之下 */
  transition: background-color 0.3s ease;
}
</style> 