<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  // 对数据进行排序，使占比大的在上面
  const sortedData = [...props.data].sort((a: any, b: any) => a.value - b.value)
  const names = sortedData.map((item: any) => item.name)
  const values = sortedData.map((item: any) => item.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}件',
      textStyle: {
        fontSize: 12,
        color: '#ffffff'
      },
      backgroundColor: 'rgba(0, 12, 47, 0.7)',
      borderColor: '#00c9d1',
      borderWidth: 1
    },
    grid: {
      left: '5%',
      right: '10%',
      bottom: '5%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: names,
      axisLabel: {
        color: '#fff',
        fontSize: 13,
        width: 145,
        overflow: 'truncate'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)'
        }
      }
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: values,
        barWidth: '30%',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: 'rgb(47, 147, 203)' },
            { offset: 1, color: 'rgb(205, 2, 242)' }
          ]),
          borderRadius: [0, 5, 5, 0]
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}件',
          fontSize: 10,
          color: '#ffffff',
          fontWeight: 'bold'
        },
        animation: true,
        animationDuration: 1500,
        animationEasing: 'elasticOut',
        animationDelay: function (idx: number) {
          return idx * 120;
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

watch(() => props.data, () => {
  if (!chart) return
  
  // 保持数据排序一致性
  const sortedData = [...props.data].sort((a: any, b: any) => b.value - a.value)
  const names = sortedData.map((item: any) => item.name)
  const values = sortedData.map((item: any) => item.value)
  
  chart.setOption({
    yAxis: {
      data: names
    },
    series: [
      {
        data: values,
        animation: true,
        animationDurationUpdate: 1000,
        animationEasingUpdate: 'elasticOut',
        animationDelayUpdate: function (idx: number) {
          return idx * 100;
        }
      }
    ]
  })
})

onMounted(() => {
  setTimeout(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  }, 300)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 185px;
  border: none;
  background-color: transparent;
}
</style> 