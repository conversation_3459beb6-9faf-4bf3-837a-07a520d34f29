<template>
  <div class="chart-container" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

// 根据数据最大值动态计算Y轴间隔
const calculateYAxisInterval = (maxValue: number) => {
  if (maxValue <= 50) {
    return 10; // 数据很少时，间隔为10
  } else if (maxValue <= 200) {
    return 50; // 数据较少时，间隔为50
  } else if (maxValue <= 1000) {
    return 200; // 数据适中时，间隔为200
  } else if (maxValue <= 5000) {
    return 1000; // 数据较多时，间隔为1000
  } else {
    return 3000; // 数据很多时，保持原来的3000间隔
  }
}

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  // 不进行排序，保持数据原有顺序
  const names = props.data.map((item: any) => item.name)
  const values = props.data.map((item: any) => item.value)
  
  // 找出最大值，用于设置yAxis的max属性
  const maxValue = Math.max(...values)
  // 计算适合的间隔
  const interval = calculateYAxisInterval(maxValue)
  // 向上取整到interval的倍数，确保max值可以被interval整除
  const roundedMax = Math.ceil(maxValue / interval) * interval
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}件',
      textStyle: {
        fontSize: 12,
        color: '#ffffff'
      },
      backgroundColor: 'rgba(0, 12, 47, 0.7)',
      borderColor: '#00c9d1',
      borderWidth: 1
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '3%',
      top: '13%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: names,
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        interval: 0,
        rotate: 0,
        formatter: function(value: string) {
          return `{dataBg|${value}}`;
        },
        rich: {
          dataBg: {
            color: '#fff',
            fontSize: 14,
            fontFamily: 'Alibaba-PuHuiTi',
            backgroundColor: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: '#29b6f6'
              }, {
                offset: 0.2,
                color: '#1976d2'
              }, {
                offset: 1,
                color: '#174a8c'
              }]
            },
            borderRadius: 999,
            padding: [3, 10],
            fontWeight: 'bold',
            borderWidth: 1,
            borderColor: 'rgba(41, 182, 246, 0.25)',
            boxShadow: '0 2px 8px 0 rgba(23, 76, 140, 0.18)'
          }
        }
      },
      axisLine: {
        show: false // 隐藏X轴线
      },
      axisTick: {
        show: false // 隐藏刻度线
      },
      splitLine: {
        show: true, // 显示竖向网格线
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'solid'
        }
      }
    },
    yAxis: {
      type: 'value',
      show: true, // 显示Y轴
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        formatter: function(value: number) {
          // 确保只显示整数
          return Math.floor(value);
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'solid'
        }
      },
      max: roundedMax, // 使用取整后的最大值
      interval: interval, // 使用计算后的间隔
      minInterval: interval // 最小间隔为interval
    },
    series: [
      {
        name: '数量',
        type: 'bar',
        data: values,
        barWidth: '35%',
        barMaxWidth: 60, // 设置柱子最大宽度为60px
        barGap: '30%', // 调整柱子之间的间距
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgb(72, 181, 197)' },
            { offset: 1, color: 'rgb(246, 221, 85)' }
          ]),
          borderRadius: [20, 20, 20, 20]
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}件',
          fontSize: 12, // 减小字体大小
          color: '#fff',
          fontWeight: 'bold',
          distance: 10
        },
        animation: true,
        animationDuration: 1500,
        animationEasing: 'elasticOut',
        animationDelay: function (idx: number) {
          return idx * 120;
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
}

watch(() => props.data, () => {
  if (!chart) return
  
  const names = props.data.map((item: any) => item.name)
  const values = props.data.map((item: any) => item.value)
  const maxValue = Math.max(...values)
  // 计算适合的间隔
  const interval = calculateYAxisInterval(maxValue)
  // 向上取整到interval的倍数
  const roundedMax = Math.ceil(maxValue / interval) * interval
  
  chart.setOption({
    xAxis: {
      data: names
    },
    yAxis: {
      max: roundedMax,
      interval: interval,
      minInterval: interval,
      axisLabel: {
        formatter: function(value: number) {
          return Math.floor(value);
        }
      }
    },
    series: [
      {
        data: values,
        animation: true,
        animationDurationUpdate: 1000,
        animationEasingUpdate: 'elasticOut',
        animationDelayUpdate: function (idx: number) {
          return idx * 100;
        }
      }
    ]
  })
}, { deep: true })

onMounted(() => {
  setTimeout(() => {
    initChart()
    window.addEventListener('resize', handleResize)
  }, 300)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 165px;
  border: none;
  background-color: transparent;
}
</style> 