<template>
  <div class="chart-wrapper">
    <!-- 装饰性圆环 -->
    <div class="decoration-rings" ref="ringsRef">
      <div class="decoration-ring ring-1"></div>
      <div class="decoration-ring ring-2"></div>
      <div class="decoration-ring ring-3"></div>
      <div class="decoration-ring ring-4"></div>
      
      <!-- 星星点点装饰 -->
      <!-- 左上角三个点 -->
      <span class="pie-dot pie-dot-1"></span>
      <span class="pie-dot pie-dot-2"></span>
      <span class="pie-dot pie-dot-3"></span>
      <!-- 右边两个点 -->
      <span class="pie-dot pie-dot-4"></span>
      <span class="pie-dot pie-dot-5"></span>
      <!-- 下面四个点 -->
      <span class="pie-dot pie-dot-6"></span>
      <span class="pie-dot pie-dot-7"></span>
      <span class="pie-dot pie-dot-8"></span>
      <span class="pie-dot pie-dot-9"></span>
    </div>
    
    <!-- 原始图表容器 -->
    <div class="chart-container" ref="chartRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  sidebarCollapsed: {
    type: Boolean,
    default: false
  }
})

const chartRef = ref<HTMLElement | null>(null)
const ringsRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null
let observer: ResizeObserver | null = null

// 为数据项设置特定颜色的函数
const processDataWithColors = (data: any[]) => {
  // 首先对数据按value值进行降序排序
  const sortedData = [...data].sort((a: any, b: any) => b.value - a.value);
  
  // 引入SVG图标路径
  const diyiIcon = new URL('@/assets/icon/diyi.svg', import.meta.url).href;
  const dierIcon = new URL('@/assets/icon/dier.svg', import.meta.url).href;
  const disanIcon = new URL('@/assets/icon/disan.svg', import.meta.url).href;
  const disiIcon = new URL('@/assets/icon/disi.svg', import.meta.url).href;
  const diwuIcon = new URL('@/assets/icon/diwu.svg', import.meta.url).href;
  const diliuIcon = new URL('@/assets/icon/diliu.svg', import.meta.url).href;
  const diqiIcon = new URL('@/assets/icon/diqi.svg', import.meta.url).href;
  const dibaIcon = new URL('@/assets/icon/diba.svg', import.meta.url).href;
  const dijiuIcon = new URL('@/assets/icon/dijiu.svg', import.meta.url).href;
  
  // 定义颜色及其对应的图标
  const colorStyles = [
    // 1. 蓝色渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(134, 198, 246)' },
        { offset: 1, color: 'rgb(49, 150, 194)' }
      ]),
      icon: diyiIcon
    },
    // 2. 蓝绿到黄色渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(54, 146, 170)' },
        { offset: 1, color: 'rgb(231, 221, 142)' }
      ]),
      icon: dierIcon
    },
    // 3. 蓝色到紫色渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(58, 128, 179)' },
        { offset: 1, color: 'rgb(191, 29, 234)' }
      ]),
      icon: disanIcon
    },
    // 4. 青色到绿色渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(35, 158, 162)' },
        { offset: 1, color: 'rgb(5, 248, 69)' }
      ]),
      icon: disiIcon
    },
    // 5. 淡蓝色渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(38, 184, 229)' },
        { offset: 1, color: 'rgb(0, 234, 255)' }
      ]),
      icon: diwuIcon
    },
    // 6. 橙黄渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(255, 179, 71)' },
        { offset: 1, color: 'rgb(255, 215, 0)' }
      ]),
      icon: diliuIcon
    },
    // 7. 紫粉渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(178, 102, 255)' },
        { offset: 1, color: 'rgb(255, 182, 193)' }
      ]),
      icon: diqiIcon
    },
    // 8. 青绿渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(0, 255, 180)' },
        { offset: 1, color: 'rgb(0, 195, 255)' }
      ]),
      icon: dibaIcon
    },
    // 9. 深蓝浅蓝渐变
    {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: 'rgb(0, 91, 234)' },
        { offset: 1, color: 'rgb(0, 198, 251)' }
      ]),
      icon: dijiuIcon
    }
  ];
  
  // 为排序后的数据分配颜色和图标
  return sortedData.map((item: any, index: number) => {
    // 使用取模运算，确保即使数据项超过colorStyles数量，也能循环使用颜色
    const colorIndex = index % colorStyles.length;
    
    // 处理名称，统一长度为7个字符，不足的用空格补齐
    let paddedName = item.name;
    // 考虑中文字符宽度，使用全角空格填充
    if (paddedName.length < 7) {
      // 计算需要填充的空格数量
      const paddingNeeded = 7 - paddedName.length;
      // 使用全角空格（\u3000）填充，比不间断空格（\u00A0）宽，更适合中文环境
      paddedName = paddedName + '\u3000'.repeat(paddingNeeded);
    }
    
    return {
      name: paddedName,
      originalName: item.name, // 保留原始名称
      value: item.value,
      // 保留percentage属性(如果存在)
      percentage: item.percentage,
      itemStyle: colorStyles[colorIndex],
      icon: colorStyles[colorIndex].icon, // 添加图标属性
      // 添加iconIndex，用于在formatter中引用
      iconIndex: index
    };
  });
};

// 辅助函数：格式化件数和百分比，统一为13个字符长度
const formatValueAndPercent = (value: number, percentStr: string): [string, string] => {
  // 创建件数文本
  const valueText = `${value}件 `;
  // 创建百分比文本
  const percentText = `${percentStr}%`;
  
  // 计算总文本长度
  const totalLength = valueText.length + percentText.length;
  
  // 如果总长度小于13，在百分比后添加全角空格填充
  if (totalLength < 13) {
    const paddingNeeded = 13 - totalLength;
    return [valueText, percentText + '\u3000'.repeat(paddingNeeded)];
  }
  
  // 已经达到或超过13个字符，不需要填充
  return [valueText, percentText];
};

const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  // 处理数据，添加颜色
  const processedData = processDataWithColors(props.data);
  
  // 计算总量，用于百分比计算
  const total = props.data.reduce((sum: number, item: any) => sum + item.value, 0);
  
  // 构建rich对象，为每个数据项创建单独的icon样式
  const richStyles: any = {
    name: {
      fontSize: 14,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'left',
      verticalAlign: 'middle',
      padding: [0, 0, 0, 0],
      fontWeight: 'bold',
      overflow: 'none',
      width: 98 // 设置固定宽度确保对齐
    },
    space: {
      height: 8,  // 控制空行的高度，即两行之间的间距
      lineHeight: 8
    },
    value: {
      fontSize: 12,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'right',
      padding: [0, 2, 0, 0]
    },
    percent: {
      fontSize: 12,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'left',
      padding: [0, 0, 0, 2]
    }
  };
  
  // 为每个数据项创建单独的icon样式
  processedData.forEach((item: any, index: number) => {
    richStyles[`icon${index}`] = {
      height: 12,
      width: 12,
      backgroundColor: {
        image: item.icon
      },
      padding: [0, 3, 0, 0],
      verticalAlign: 'bottom'
    };
  });
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        // 使用原始名称而不是填充后的名称，避免显示填充的空格
        return `${params.data.originalName}:${params.value}(${params.percent}%)`;
      }
    },
    legend: {
      show: false // 隐藏图例，因为我们使用引导线标签展示数据
    },
    graphic: [
      {
        type: 'text',
        left: '47%', // 与饼图中心精确对齐
        top: '40%',  // 垂直位置
        z: 10,       // 确保文本在最上层
        style: {
          text: '总数',
          textAlign: 'center',
          fill: '#00fffc',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      {
        type: 'rect',
        left: '47.5%', // 与饼图中心精确对齐
        top: '47.5%', // 总数和件数之间
        z: 10,
        shape: {
          width: 25,
          height: 4
        },
        style: {
          fill: 'rgb(34, 64, 174)'
        }
      },
      {
        type: 'text',
        left: '47.5%', // 与饼图中心精确对齐
        top: '53%',  // 垂直位置
        z: 10,       // 确保文本在最上层
        style: {
          text: total + '件',
          textAlign: 'center',
          fill: '#ffffff',
          fontSize: 13
        }
      }
    ],
    series: [
      {
        name: '涉检情况',
        type: 'pie',
        center: ['50%', '50%'], // 居中显示
        radius: ['30.6%', '53.55%'], // 再调小10%
        avoidLabelOverlap: true, // 避免标签重叠
        padAngle: 5,
        label: {
          show: true,
          edgeDistance: 30,
          position: 'outside',
          backgroundColor: 'rgba(16, 89, 157, 0.7)',
          borderRadius: 8,
          padding: [8, 12, 8, 20],
          overflow: 'none',
          width: 90,
          height: 30,
          formatter: function(params: any) {
            // 计算百分比值 - 如果传入的数据已经包含percentage，则直接使用
            // 否则基于value计算百分比
            let percent: string;
            if (params.data.percentage !== undefined) {
              // 如果percentage是数值（小数形式），转换为百分比形式
              percent = (params.data.percentage * 100).toFixed(1);
            } else {
              percent = ((params.value / total) * 100).toFixed(1);
            }
            
            // 格式化件数和百分比，使总长度为13个字符
            const [valueText, percentText] = formatValueAndPercent(params.value, percent);
            
            // 使用data中保存的iconIndex，确保每个标签使用正确的图标
            return [
              `{icon${params.data.iconIndex}|}{name|${params.name}}`,
              `{space|}`,  // 添加一个空行增加间距
              `{value|${valueText}}{percent|${percentText}}`
            ].join('\n');
          },
          rich: richStyles
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
            color: '#fff'
          }
        },
        labelLine: {
          show: true,
          length: 20,   // 第一段线条长度从15增加到30
          length2: 25,  // 第二段线条长度从15增加到40
          smooth: true,
          lineStyle: {
            color: '#5AB4ED',
            width: 1.5
          }
        },
        data: processedData,
        itemStyle: {
          borderWidth: 0, // 移除边框
          borderColor: '#000c2f'
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const handleResize = () => {
  chart?.resize()
  // 调整装饰环位置，确保与图表中心对齐
  updateRingsPosition()
}

// 更新装饰环位置的函数 - 优化计算逻辑
const updateRingsPosition = () => {
  if (!chartRef.value || !ringsRef.value || !chart) return
  
  // 使用requestAnimationFrame来确保在下一次重绘前更新位置
  requestAnimationFrame(() => {
    // 确保DOM元素仍然存在
    if (!chartRef.value || !ringsRef.value) return
    
    // 获取echarts容器的中心位置
    const chartRect = chartRef.value.getBoundingClientRect()
    const chartWidth = chartRect.width
    const chartHeight = chartRect.height
    const chartCenterX = chartWidth / 2
    const chartCenterY = chartHeight / 2
    
    // 计算基准尺寸 - 使用宽度与高度的较小值作为参考，确保圆环尺寸稳定
    const baseSize = Math.min(chartWidth, chartHeight)
    
    // 设置装饰环的位置，使其与echarts的中心重合
    ringsRef.value.style.setProperty('--ring-center-x', `${chartCenterX}px`)
    ringsRef.value.style.setProperty('--ring-center-y', `${chartCenterY}px`)
    
    // 设置基准尺寸变量，用于动态尺寸计算
    ringsRef.value.style.setProperty('--base-size', `${baseSize}px`)
    
    // 保留这些变量用于其他用途
    ringsRef.value.style.setProperty('--chart-width', `${chartWidth}px`)
    ringsRef.value.style.setProperty('--chart-height', `${chartHeight}px`)
  })
}

// 多次检查装饰环位置，确保在DOM完全更新后位置正确
const ensureCorrectPosition = () => {
  // 立即执行一次
  updateRingsPosition()
  
  // 然后在短时间内多次检查，确保位置正确
  setTimeout(updateRingsPosition, 50)
  setTimeout(updateRingsPosition, 150)
  setTimeout(updateRingsPosition, 300)
  setTimeout(updateRingsPosition, 500)
}

// 监听侧边栏状态变化 - 增强处理逻辑
watch(() => props.sidebarCollapsed, () => {
  // 当侧边栏状态变化时，使用多次检查确保装饰环位置正确
  nextTick(() => {
    ensureCorrectPosition()
    
    // 强制调整图表大小以适应新布局
    chart?.resize()
    
    // 在侧边栏过渡动画完成后再次检查位置
    setTimeout(() => {
      updateRingsPosition()
      chart?.resize() // 再次调整图表大小
    }, 350) // 侧边栏过渡动画是300ms，我们等待稍长一点
  })
})

watch(() => props.data, () => {
  if (!chart) return
  
  // 处理数据，添加颜色
  const processedData = processDataWithColors(props.data);
  
  // 计算总量
  const total = props.data.reduce((sum: number, item: any) => sum + item.value, 0);
  
  // 构建rich对象，为每个数据项创建单独的icon样式
  const richStyles: any = {
    name: {
      fontSize: 14,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'left',
      verticalAlign: 'middle',
      padding: [0, 0, 0, 0],
      fontWeight: 'bold',
      overflow: 'none',
      width: 98 // 设置固定宽度确保对齐
    },
    space: {
      height: 8,  // 控制空行的高度，即两行之间的间距
      lineHeight: 8
    },
    value: {
      fontSize: 12,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'right',
      padding: [0, 2, 0, 0]
    },
    percent: {
      fontSize: 12,
      fontFamily: 'Alibaba-PuHuiTi',
      color: '#fff',
      align: 'left',
      padding: [0, 0, 0, 2]
    }
  };
  
  // 为每个数据项创建单独的icon样式
  processedData.forEach((item: any, index: number) => {
    richStyles[`icon${index}`] = {
      height: 12,
      width: 12,
      backgroundColor: {
        image: item.icon
      },
      padding: [0, 3, 0, 0],
      verticalAlign: 'bottom'
    };
  });
  
  chart.setOption({
    legend: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        // 使用原始名称而不是填充后的名称，避免显示填充的空格
        return `${params.data.originalName}:${params.value}(${params.percent}%)`;
      }
    },
    graphic: [
      {
        type: 'text',
        left: '50%',
        top: '40%',
        z: 10,
        style: {
          text: '总数',
          textAlign: 'center',
          fill: '#00fffc',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      {
        type: 'rect',
        left: '50%',
        top: '47.5%',
        z: 10,
        shape: {
          width: 25,
          height: 4
        },
        style: {
          fill: 'rgb(34, 64, 174)'
        }
      },
      {
        type: 'text',
        left: '50%',
        top: '55%',
        z: 10,
        style: {
          text: total + '件',
          textAlign: 'center',
          fill: '#ffffff',
          fontSize: 14
        }
      }
    ],
    series: [
      {
        data: processedData,
        center: ['50%', '50%'],
        radius: ['30.6%', '53.55%'], // 再调小10%
        label: {
          show: true,
          edgeDistance: 40,
          backgroundColor: 'rgba(16, 89, 157, 0.7)',
          borderRadius: 8,
          padding: [8, 12, 8, 12],
          overflow: 'none',
          width: 120,
          formatter: function(params: any) {
            // 计算百分比值 - 如果传入的数据已经包含percentage，则直接使用
            // 否则基于value计算百分比
            let percent: string;
            if (params.data.percentage !== undefined) {
              // 如果percentage是数值（小数形式），转换为百分比形式
              percent = (params.data.percentage * 100).toFixed(1);
            } else {
              percent = ((params.value / total) * 100).toFixed(1);
            }
            
            // 格式化件数和百分比，使总长度为13个字符
            const [valueText, percentText] = formatValueAndPercent(params.value, percent);
            
            // 使用data中保存的iconIndex，确保每个标签使用正确的图标
            return [
              `{icon${params.data.iconIndex}|}{name|${params.name}}`,
              `{space|}`,  // 添加一个空行增加间距
              `{value|${valueText}}{percent|${percentText}}`
            ].join('\n');
          },
          rich: richStyles
        },
        labelLine: {
          show: true,
          length: 20,   // 第一段线条长度从15增加到30
          length2: 25,  // 第二段线条长度从15增加到40
          smooth: true,
          lineStyle: {
            color: '#5AB4ED',
            width: 1.5
          }
        },
        itemStyle: {
          borderWidth: 0, // 移除边框
          borderColor: '#000c2f'
        }
      }
    ]
  })
  
  // 更新后重新调整装饰环位置
  nextTick(() => {
    updateRingsPosition()
  })
})

onMounted(() => {
  setTimeout(() => {
    initChart()
    
    // 初始化后调整装饰环位置
    nextTick(() => {
      ensureCorrectPosition()
    })
    
    // 使用ResizeObserver监听容器大小变化
    if (chartRef.value) {
      observer = new ResizeObserver(() => {
        handleResize()
        // 在调整大小后，确保再次检查位置
        setTimeout(updateRingsPosition, 100)
      })
      observer.observe(chartRef.value)
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }, 300)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
  
  // 清理ResizeObserver
  if (observer) {
    observer.disconnect()
    observer = null
  }
})
</script>

<style scoped>
.chart-wrapper {
  position: relative;
  width: 100%;
  height: 290px;
  min-height: 250px;
  max-height: 330px;
  overflow: hidden; /* 防止内容溢出 */
  box-sizing: border-box; /* 确保padding和border不会增加元素总宽度 */
  isolation: isolate; /* 创建新的层叠上下文，防止溢出影响 */
}

/* 添加全局样式处理 */
:deep(.case-analysis-content) {
  overflow-x: hidden; /* 防止水平滚动 */
}

.chart-container {
  width: 100%;
  height: 100%;
  border: none;
  background-color: transparent;
  overflow: visible;
  position: relative;
  z-index: 1;
  box-sizing: border-box; /* 确保padding和border不会增加元素总宽度 */
}

/* 装饰环容器 */
.decoration-rings {
  position: absolute;
  /* 使用CSS变量定位圆心位置 */
  left: var(--ring-center-x, 50%);
  top: var(--ring-center-y, 50%);
  transform: translate(-50%, -50%);
  z-index: 0;
  pointer-events: none; /* 确保鼠标事件穿透到下层 */
  width: 0;
  height: 0;
  transform-origin: center center;
  will-change: transform; /* 优化性能 */
  contain: layout; /* 性能优化 */
}

/* 装饰性圆环公共样式 */
.decoration-ring {
  position: absolute;
  border-radius: 50%;
  box-sizing: border-box;
  left: 0;
  top: 0;
  transform: translate(-50%, -50%);
  z-index: 0;
  /* 确保不会创建额外的层 */
  will-change: transform;
  contain: layout;
}

/* 第一个圆环 - 使用相对单位 */
.ring-1 {
  /* 使用基准尺寸计算，确保尺寸稳定 */
  width: calc(var(--base-size, 100%) * 0.7);
  height: calc(var(--base-size, 100%) * 0.7);
  border: 2px solid rgb(27, 86, 127);
  /* 使用conic-gradient实现平滑过渡 */
  mask: conic-gradient(from 0deg, 
                    black 0deg, 
                    black 60deg, 
                    rgba(0, 0, 0, 0.9) 70deg,
                    rgba(0, 0, 0, 0.8) 80deg,
                    rgba(0, 0, 0, 0.6) 90deg,
                    rgba(0, 0, 0, 0.4) 100deg,
                    rgba(0, 0, 0, 0.2) 110deg,
                    transparent 120deg,
                    transparent 180deg,
                    rgba(0, 0, 0, 0.2) 190deg,
                    rgba(0, 0, 0, 0.4) 200deg,
                    rgba(0, 0, 0, 0.6) 210deg,
                    rgba(0, 0, 0, 0.8) 220deg,
                    rgba(0, 0, 0, 0.9) 230deg,
                    black 240deg,
                    black 360deg);
  -webkit-mask: conic-gradient(from 0deg, 
                    black 0deg, 
                    black 60deg, 
                    rgba(0, 0, 0, 0.9) 70deg,
                    rgba(0, 0, 0, 0.8) 80deg,
                    rgba(0, 0, 0, 0.6) 90deg,
                    rgba(0, 0, 0, 0.4) 100deg,
                    rgba(0, 0, 0, 0.2) 110deg,
                    transparent 120deg,
                    transparent 180deg,
                    rgba(0, 0, 0, 0.2) 190deg,
                    rgba(0, 0, 0, 0.4) 200deg,
                    rgba(0, 0, 0, 0.6) 210deg,
                    rgba(0, 0, 0, 0.8) 220deg,
                    rgba(0, 0, 0, 0.9) 230deg,
                    black 240deg,
                    black 360deg);
}

/* 第二个圆环 - 使用相对单位 */
.ring-2 {
  /* 使用基准尺寸计算，确保尺寸稳定 */
  width: calc(var(--base-size, 100%) * 0.60);
  height: calc(var(--base-size, 100%) * 0.60);
  border: 2px solid rgb(50, 134, 195);
  /* 使用conic-gradient实现平滑过渡 */
  mask: conic-gradient(from 0deg, 
                    black 0deg, 
                    black 240deg, 
                    rgba(0, 0, 0, 0.9) 250deg,
                    rgba(0, 0, 0, 0.8) 260deg,
                    rgba(0, 0, 0, 0.6) 270deg,
                    rgba(0, 0, 0, 0.4) 280deg,
                    rgba(0, 0, 0, 0.2) 290deg,
                    transparent 300deg, 
                    transparent 360deg);
  -webkit-mask: conic-gradient(from 0deg, 
                    black 0deg, 
                    black 240deg, 
                    rgba(0, 0, 0, 0.9) 250deg,
                    rgba(0, 0, 0, 0.8) 260deg,
                    rgba(0, 0, 0, 0.6) 270deg,
                    rgba(0, 0, 0, 0.4) 280deg,
                    rgba(0, 0, 0, 0.2) 290deg,
                    transparent 300deg, 
                    transparent 360deg);
}

/* 第三个圆环 - 使用相对单位 */
.ring-3 {
  /* 使用基准尺寸计算，确保尺寸稳定 */
  width: calc(var(--base-size, 100%) * 0.63);
  height: calc(var(--base-size, 100%) * 0.63);
  border:  54px solid rgba(25, 57, 130, 0.5); /* 调整边框宽度 */
}

.ring-4 {
  /* 使用基准尺寸计算，确保尺寸稳定 */
  width: calc(var(--base-size, 100%) * 0.29);
  height: calc(var(--base-size, 100%) * 0.29);
  border: 8px solid rgb(32, 83, 139);
  mask: conic-gradient(
    from 0deg,
    black 0deg, black 15deg,
    transparent 15deg, transparent 20deg,
    black 20deg, black 35deg,
    transparent 35deg, transparent 40deg,
    black 40deg, black 55deg,
    transparent 55deg, transparent 60deg,
    black 60deg, black 75deg,
    transparent 75deg, transparent 80deg,
    black 80deg, black 95deg,
    transparent 95deg, transparent 100deg,
    black 100deg, black 115deg,
    transparent 115deg, transparent 120deg,
    black 120deg, black 135deg,
    transparent 135deg, transparent 140deg,
    black 140deg, black 155deg,
    transparent 155deg, transparent 160deg,
    black 160deg, black 175deg,
    transparent 175deg, transparent 180deg,
    black 180deg, black 195deg,
    transparent 195deg, transparent 200deg,
    black 200deg, black 215deg,
    transparent 215deg, transparent 220deg,
    black 220deg, black 235deg,
    transparent 235deg, transparent 240deg,
    black 240deg, black 255deg,
    transparent 255deg, transparent 260deg,
    black 260deg, black 275deg,
    transparent 275deg, transparent 280deg,
    black 280deg, black 295deg,
    transparent 295deg, transparent 300deg,
    black 300deg, black 315deg,
    transparent 315deg, transparent 320deg,
    black 320deg, black 335deg,
    transparent 335deg, transparent 340deg,
    black 340deg, black 355deg,
    transparent 355deg, transparent 360deg
  );
  -webkit-mask: conic-gradient(
    from 0deg,
    black 0deg, black 15deg,
    transparent 15deg, transparent 20deg,
    black 20deg, black 35deg,
    transparent 35deg, transparent 40deg,
    black 40deg, black 55deg,
    transparent 55deg, transparent 60deg,
    black 60deg, black 75deg,
    transparent 75deg, transparent 80deg,
    black 80deg, black 95deg,
    transparent 95deg, transparent 100deg,
    black 100deg, black 115deg,
    transparent 115deg, transparent 120deg,
    black 120deg, black 135deg,
    transparent 135deg, transparent 140deg,
    black 140deg, black 155deg,
    transparent 155deg, transparent 160deg,
    black 160deg, black 175deg,
    transparent 175deg, transparent 180deg,
    black 180deg, black 195deg,
    transparent 195deg, transparent 200deg,
    black 200deg, black 215deg,
    transparent 215deg, transparent 220deg,
    black 220deg, black 235deg,
    transparent 235deg, transparent 240deg,
    black 240deg, black 255deg,
    transparent 255deg, transparent 260deg,
    black 260deg, black 275deg,
    transparent 275deg, transparent 280deg,
    black 280deg, black 295deg,
    transparent 295deg, transparent 300deg,
    black 300deg, black 315deg,
    transparent 315deg, transparent 320deg,
    black 320deg, black 335deg,
    transparent 335deg, transparent 340deg,
    black 340deg, black 355deg,
    transparent 355deg, transparent 360deg
  );
}

.arrow-icon {
    position: relative;
    width: 15px;
    height: 15px;
}

.arrow-icon::before, .arrow-icon::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 50%;
    background-color: #3498db;
}

.arrow-icon::before {
    top: 0;
    transform: skew(25deg, 0deg);
    transform-origin: top left;
}

.arrow-icon::after {
    bottom: 0;
    transform: skew(-25deg, 0deg);
    transform-origin: bottom left;
}

/* 星星点点 - 调整定位方式，使用相对单位，位置更靠近圆环 */
.pie-dot {
  position: absolute;
  border-radius: 50%;
  z-index: 2;
  transform-origin: center center;
  will-change: transform;
}

/* 左上角三个点 - 位置调整更靠近圆环 */
.pie-dot-1 {
  width: 6px;
  height: 6px;
  background-color: rgb(48, 165, 241);
  left: calc(var(--base-size, 100%) * -0.29);
  top: calc(var(--base-size, 100%) * -0.25);
}

.pie-dot-2 {
  width: 3px;
  height: 3px;
  background-color: rgb(249, 253, 253);
  left: calc(var(--base-size, 100%) * -0.03);
  top: calc(var(--base-size, 100%) * -0.32);
}

.pie-dot-3 {
  width: 2px;
  height: 2px;
  background-color: rgba(243, 244, 245, 0.9);
  left: calc(var(--base-size, 100%) * -0.18);
  top: calc(var(--base-size, 100%) * -0.25);
}

/* 右边两个点 - 位置调整更靠近圆环 */
.pie-dot-4 {
  width: 5px;
  height: 5px;
  background-color: rgb(250, 252, 253);
  left: calc(var(--base-size, 100%) * 0.32);
  top: calc(var(--base-size, 100%) * -0.03);
}

.pie-dot-5 {
  width: 3px;
  height: 3px;
  background-color: rgb(91, 192, 255);
  left: calc(var(--base-size, 100%) * 0.22);
  top: calc(var(--base-size, 100%) * -0.2);
}

/* 下面四个点 - 位置调整更靠近圆环 */
.pie-dot-6 {
  width: 3px;
  height: 3px;
  background-color: rgb(48, 165, 241);
  left: calc(var(--base-size, 100%) * -0.30);
  top: calc(var(--base-size, 100%) * 0.15);
}

.pie-dot-7 {
  width: 3px;
  height: 3px;
  background-color: rgb(249, 253, 253);
  left: calc(var(--base-size, 100%) * -0.10);
  top: calc(var(--base-size, 100%) * 0.26);
}

.pie-dot-8 {
  width: 4px;
  height: 4px;
  background-color: rgba(243, 244, 245, 0.9);
  left: calc(var(--base-size, 100%) * 0.10);
  top: calc(var(--base-size, 100%) * 0.30);
}

.pie-dot-9 {
  width: 2px;
  height: 2px;
  background-color: rgb(48, 165, 241);
  left: calc(var(--base-size, 100%) * 0.20);
  top: calc(var(--base-size, 100%) * 0.19);
}
</style> 