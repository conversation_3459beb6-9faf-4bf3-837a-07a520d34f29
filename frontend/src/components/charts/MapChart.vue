<template>
  <div class="chart-container map-chart" ref="chartRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import guangzhouJson from '@/assets/guangzhou.json'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

// 上升/下降箭头的Unicode字符
const UP_ARROW = '\u2191'; // ↑
const DOWN_ARROW = '\u2193'; // ↓

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  // 处理地图数据
  try {
    // 确保地图格式正确
    const geoJSON = {
      type: 'FeatureCollection',
      features: guangzhouJson.features.map((feature: any) => ({
        type: 'Feature',
        properties: { name: feature.properties.name },
        geometry: feature.geometry
      }))
    }
    
    // 注册地图
    echarts.registerMap('guangzhou', geoJSON as any)
    
    chart = echarts.init(chartRef.value)
    
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          // 获取tb值
          const tb = params.data.tb;
          
          // 基本信息
          let result = `${params.name}: ${params.value}件`;
          
          // 如果存在tb值，添加同比信息
          if (tb !== undefined) {
            // 决定箭头方向和颜色
            const arrow = tb >= 0 ? UP_ARROW : DOWN_ARROW;
            const color = tb >= 0 ? '#FF4500' : '#32CD32';  // 上升为红色，下降为绿色
            
            // 格式化tb值，保留一位小数，并添加%符号
            const tbValue = Math.abs(tb).toFixed(1) + '%';
            
            // 添加同比信息到tooltip
            result += `<br/>同比 <span style="color:${color}; font-weight:bold;">${arrow} ${tbValue}</span>`;
          }
          
          return result;
        },
        backgroundColor: 'rgba(0, 12, 47, 0.7)',
        borderColor: '#00c9d1',
        borderWidth: 1,
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        padding: [8, 12]
      },
      visualMap: {
        show: false, // 隐藏visualMap控件
        min: 0,
        max: 1600,
        left: 'left',
        bottom: '5%',
        text: ['高', '低'],
        calculable: true,
        inRange: {
          color: ['#0468d1']
        },
        textStyle: {
          color: '#fff'
        }
      },
      series: [
        {
          name: '信访量',
          type: 'map',
          map: 'guangzhou',
          roam: false,
          zoom: 1.2,
          center: [113.5, 23.2],
          selectedMode: false,
          label: {
            show: true,
            color: '#fff',
            fontSize: 12,
            formatter: function(params: any) {
              return params.name + '\n' + params.value + '件';
            }
          },
          itemStyle: {
            areaColor: '#0468d1',
            borderColor: '#00c9d1',
            borderWidth: 1
          },
          emphasis: {
            label: {
              show: true,
              color: '#fff',
              fontSize: 14,
              fontWeight: 'bold'
            },
            itemStyle: {
              areaColor: '#00fffc'
            }
          },
          data: props.data
        }
      ]
    }
    
    chart.setOption(option)
    
    // 立即执行一次resize以确保地图正确渲染
    setTimeout(() => {
      chart?.resize()
    }, 200)
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
}

const handleResize = () => {
  chart?.resize()
}

watch(() => props.data, () => {
  chart?.setOption({
    series: [
      {
        data: props.data
      }
    ]
  })
})

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  chart?.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 660px;
}
</style> 