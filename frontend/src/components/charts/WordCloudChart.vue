<template>
  <div class="word-cloud-container" ref="containerRef">
    <div 
      v-for="(item, index) in processedData" 
      :key="index"
      class="word-item"
      :style="getItemStyle(item)"
      :data-start-y="item.startY"
      :data-target-y="item.y"
      @mouseover="showTooltip($event, item)"
      @mouseout="hideTooltip"
    >
      <span class="dot dot-1"></span>
      <span class="dot dot-2"></span>
      <span class="dot dot-3"></span>
      <span class="dot dot-4"></span>
      <span class="dot dot-5"></span>
      {{ item.name }}
    </div>
  </div>
  
  <!-- 使用Teleport将提示框传送到body -->
  <teleport to="body">
    <div 
      class="word-tooltip" 
      ref="tooltipRef"
      :style="tooltipStyle"
      v-show="showingTooltip"
    >
      <div class="tooltip-content">
        <div class="tooltip-name">{{ tooltipData.name }}</div>
        <div class="tooltip-value">{{ tooltipData.value }}</div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const containerRef = ref<HTMLElement | null>(null)
let animationTimer: number | null = null
const containerWidth = ref(600)  // 默认宽度
const containerHeight = ref(290) // 默认高度

// 提示框相关状态
const tooltipRef = ref<HTMLElement | null>(null)
const showingTooltip = ref(false)
const tooltipStyle = ref({
  left: '0px',
  top: '0px'
})
const tooltipData = ref({
  name: '',
  value: 0
})

// 显示提示框
const showTooltip = (event: MouseEvent, item: any) => {
  if (!tooltipRef.value) return
  
  // 设置提示框内容
  tooltipData.value = {
    name: item.name,
    value: item.value
  }
  
  // 计算提示框位置，相对于视口定位
  const targetElement = event.currentTarget as HTMLElement
  const rect = targetElement.getBoundingClientRect()
  
  // 使用视口坐标，而不是相对于容器的坐标
  const left = rect.left + rect.width / 2
  const top = rect.top - 60 // 在词条上方60px处显示
  
  tooltipStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  }
  
  // 显示提示框
  showingTooltip.value = true
}

// 隐藏提示框
const hideTooltip = () => {
  showingTooltip.value = false
}

// 处理数据，重新计算位置和大小
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) return []
  
  // 首先重新计算所有元素的位置
  const result = []
  const positions: {x: number, y: number, width: number, height: number}[] = []
  const maxItems = 50 // 最大显示数量
  
  // 按value排序，确保重要的词先放置
  const sortedData = [...props.data].sort((a: any, b: any) => b.value - a.value)
  
  // 计算最大值和最小值，用于归一化
  const values = sortedData.map((item: any) => item.value)
  const maxValue = Math.max(...values)
  const minValue = Math.min(...values)
  const valueRange = maxValue - minValue
  
  // 增加一个对数映射的值计算
  const logValues = values.map(value => Math.log10(value + 1))
  const maxLogValue = Math.max(...logValues)
  const minLogValue = Math.min(...logValues)
  const logValueRange = maxLogValue - minLogValue
  
  for (let i = 0; i < Math.min(sortedData.length, maxItems); i++) {
    const item: any = sortedData[i]
    
    // 结合线性归一化和对数归一化的结果
    // 对于数值差异大的数据，对数归一化会让小值更加明显
    let normalizedValue = 0;
    if (valueRange > 0) {
      // 线性归一化
      const linearNormalized = (item.value - minValue) / valueRange;
      
      // 对数归一化
      const logNormalized = logValueRange > 0 
        ? (Math.log10(item.value + 1) - minLogValue) / logValueRange 
        : 0.5;
      
      // 对于数值差异大的数据，加大对数映射的权重
      // 这里使用最大值与最小值的比例作为判断依据
      const logWeight = maxValue / Math.max(minValue, 1) > 20 ? 0.7 : 0.3;
      
      // 加权结合两种归一化结果
      normalizedValue = linearNormalized * (1 - logWeight) + logNormalized * logWeight;
    } else {
      normalizedValue = 0.5;
    }
    
    const size = calculateSize(normalizedValue, item.name.length)
    const animationDuration = 1 + Math.random() * 0.5 // 1-1.5秒之间
    
    // 计算新位置
    let attempts = 0
    let position
    
    do {
      // 随机位置，但确保在容器内
      const x = Math.random() * (containerWidth.value - size)
      const y = Math.random() * (containerHeight.value - size)
      
      position = {
        x: x + size/2,
        y: y + size/2,
        width: size * 2,
        height: size * 2
      }
      
      attempts++
      
      // 如果尝试了太多次还找不到位置，就跳过这个元素
      if (attempts > 50) break
      
    } while (isOverlapping(position, positions) && attempts < 50)
    
    // 如果找到了合适的位置
    if (attempts < 50) {
      positions.push(position)
      
      // 为每个元素计算独特的起始位置和目标位置
      // 目标位置就是当前计算的位置
      const targetY = position.y
      // 修改：起始位置随机在目标位置上下5-10像素范围内浮动
      const startY = targetY + (Math.random() * 10 - 5)
      
      result.push({
        name: item.name,
        value: item.value,
        x: position.x,
        y: targetY,         // 目标位置
        startY: startY,     // 起始位置
        size: size,
        animationDuration: animationDuration,
        animationDelay: Math.random() * 0.5,  // 随机延迟，最大0.5秒
        // 添加浮动方向标记，用于确定元素是向上还是向下浮动
        floatDirection: Math.random() > 0.5 ? 'up' : 'down'
      })
    }
  }
  
  return result
})

// 检查是否与已有元素重叠
function isOverlapping(position: {x: number, y: number, width: number, height: number}, 
                       positions: {x: number, y: number, width: number, height: number}[]) {
  // 减少重叠要求，允许一些重叠
  const overlapThreshold = 0.7 
  
  for (const pos of positions) {
    const dx = Math.abs(position.x - pos.x)
    const dy = Math.abs(position.y - pos.y)
    const minDistance = (position.width + pos.width) * 0.5 * overlapThreshold
    
    if (dx < minDistance && dy < minDistance) {
      return true // 重叠
    }
  }
  
  return false
}

// 根据归一化的值和名称长度计算大小
function calculateSize(normalizedValue: number, nameLength: number) {
  // 使用对数映射代替线性映射，让差异更明显
  // 避免log(0)的情况，确保最小值至少为0.01
  const logValue = normalizedValue <= 0 ? 0 : Math.log10(normalizedValue * 9 + 1) / Math.log10(10);
  
  // 调整基础大小范围，从18到63，扩大视觉差异
  // 小值元素基础大小大幅减小，大值元素保持不变
  const baseSize = 22 + logValue * 45;
  
  // 减少名称长度对大小的影响
  const nameFactor = Math.min(nameLength * 0.5, 10);
  
  // 确保元素大小在合理范围内
  // 最小值和最大值都增大
  return Math.max(16, Math.min(72, baseSize + nameFactor));
}

// 获取元素样式
function getItemStyle(item: any) {
  // 计算居中位置
  const halfWidth = item.size * 0.8
  const halfHeight = item.size * 0.8
  
  // 基本样式
  const style: any = {
    left: `${item.x - halfWidth}px`,
    top: `${item.y - halfHeight}px`,
    fontSize: `${Math.max(12, item.size * 0.34)}px`, // 增大最小字体大小
    width: `${item.size * 1.6}px`,
    height: `${item.size * 1.6}px`,
    lineHeight: `${Math.max(16, item.size * 0.4)}px`, // 增大最小行高
    zIndex: Math.floor(item.value),  // 值越大的元素显示在上层
    '--animation-duration': `${item.animationDuration}s`,
    '--animation-delay': `${item.animationDelay}s`,
    '--start-y': `${item.startY - item.y}px`,  // 起始位置的偏移量
    '--float-amplitude': `${5 + Math.random() * 5}px`, // 添加浮动幅度，5-10像素
    '--float-direction': item.floatDirection // 浮动方向
  }
  
  return style
}

// 测量容器尺寸
function measureContainer() {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.clientWidth || 600
    containerHeight.value = containerRef.value.clientHeight || 290
  }
}

// 开始动画
function startAnimation() {
  // 获取所有词云元素
  const wordItems = document.querySelectorAll('.word-item')
  
  // 首先移除所有动画类
  wordItems.forEach((item) => {
    item.classList.remove('floating-up', 'floating-down')
  })
  
  // 强制重绘
  void document.body.offsetHeight
  
  // 添加动画类以启动动画，根据方向添加不同的动画类
  wordItems.forEach((item) => {
    // 将Element类型转换为HTMLElement类型
    const element = item as HTMLElement
    const direction = element.style.getPropertyValue('--float-direction') || 'up'
    if (direction === 'up') {
      item.classList.add('floating-up')
    } else {
      item.classList.add('floating-down')
    }
  })
  
  // 移除动画重置机制，让CSS动画自行循环
  // 不再需要定时器重置动画
  if (animationTimer) {
    clearTimeout(animationTimer)
    animationTimer = null
  }
}

// 监听数据变化
watch(() => props.data, () => {
  // 数据变化时重新开始动画
  if (animationTimer) {
    clearTimeout(animationTimer)
    animationTimer = null
  }
  
  // 延迟一小段时间后开始动画
  setTimeout(() => {
    startAnimation()
  }, 300)
})

onMounted(() => {
  // 组件挂载后测量容器尺寸并开始动画
  setTimeout(() => {
    measureContainer()
    startAnimation()
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', () => {
      measureContainer()
    })
  }, 300)
})

onUnmounted(() => {
  // 清除定时器和事件监听
  if (animationTimer) {
    clearTimeout(animationTimer)
    animationTimer = null
  }
  
  window.removeEventListener('resize', measureContainer)
})
</script>

<style>
/* 提示框全局样式 */
.word-tooltip {
  position: fixed; /* 使用固定定位，相对于视口 */
  z-index: 9999; /* 非常高的z-index值 */
  transform: translateX(-50%);
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.tooltip-content {
  background: radial-gradient(circle at 10% 10%, 
                             rgba(255, 255, 255, 0.3) 0%, 
                             rgba(30, 81, 123, 0.95) 50%, 
                             rgba(27, 54, 151, 0.95) 100%);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(67, 177, 255, 0.8);
  text-align: center;
  min-width: 80px;
  backdrop-filter: blur(2px); /* 添加模糊效果增强可见性 */
}

.tooltip-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.tooltip-value {
  font-size: 14px;
  color: #00eaff;
  text-shadow: 0 0 8px rgba(0, 234, 255, 0.9);
}

.tooltip-arrow {
  position: absolute;
  bottom: -25px; /* 增加箭头长度 */
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
  background-color: rgba(67, 177, 255, 0.8);
  z-index: 999;
}

.tooltip-arrow:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(67, 177, 255, 0.8);
}
</style>

<style scoped>
.word-cloud-container {
  position: relative;
  width: 100%;
  height: 290px;
  min-height: 200px;
  max-height: 300px;
  overflow: visible; /* 允许元素溢出容器 */
}

.word-item {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: #fff;
  font-weight: bold;
  text-align: center;
  /* 应用与circle-gradient-text相同的渐变效果 */
  background: 
    radial-gradient(circle at 10% 10%, #fff 0%, #eaf4ff 10%, transparent 60%),
    radial-gradient(circle at 5% 5%, #b2cbe9 0%, #1c4289 45%, #3b6eff 100%),
    linear-gradient(135deg, #fff 0%, transparent 60%);
  box-shadow: inset 0 2px 18px 0 #1a357a80;
  opacity: 0.9;
  padding: 6px; /* 减小内边距 */
  white-space: normal; /* 允许文本换行 */
  word-break: break-word; /* 允许在单词内换行 */
  overflow: visible; /* 改为visible以显示圆环和点点 */
  box-sizing: border-box;
  transform: translateY(var(--start-y, 40px));
  hyphens: auto; /* 添加连字符 */
  text-shadow: 0 2px 8px #1a357a80;
  user-select: none;
}

/* 添加圆环效果 */
.word-item::before,
.word-item::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #0184fc;
}

.word-item::after {
  width: calc(100% + 2px);
  height: calc(100% + 2px);
}

/* 星星点点样式 */
.dot {
  position: absolute;
  border-radius: 50%;
  z-index: 2; /* 确保显示在圆环之上 */
  background-color: rgba(149, 225, 255, 1);
}

/* 不同大小和位置的星星点点 */
.dot-1 {
  width: 4px;
  height: 4px;
  top: 10%;
  left: 10%; /* 左上角一个大点 */
  background-color: rgb(48, 165, 241); /* 亮白色 */
}

.dot-2 {
  width: 4px;
  height: 4px;
  bottom: 15%;
  right: 2%; /* 右下角一个小点 */
  background-color: rgba(249, 253, 253); /* 蓝色 */
}

.dot-3 {
  width: 3px;
  height: 3px;
  bottom: 20%;
  left: 0%; /* 右下角第二个更小的点 */
  background-color: rgb(249, 253, 253); /* 青色 */
}

.dot-4 {
  width: 2px;
  height: 2px;
  top: 15%;
  right: 5%; /* 右侧一个最小的点 */
  background-color: rgba(243, 244, 245, 0.9); /* 淡蓝色 */
}

.dot-5 {
  width: 3px;
  height: 3px;
  bottom: 50%;
  right: -6%; /* 右下角第二个更小的点 */
  background-color: rgb(48, 165, 241); /* 青色 */
}

.word-item.floating-up {
  animation-name: floatUpDown;
  animation-timing-function: ease-in-out;
  animation-duration: 6s; /* 总时长6秒，包含上下运动和停留时间 */
  animation-delay: var(--animation-delay, 0s);
  animation-iteration-count: infinite; /* 无限循环 */
  animation-direction: normal;
  animation-fill-mode: both;
}

.word-item.floating-down {
  animation-name: floatDownUp;
  animation-timing-function: ease-in-out;
  animation-duration: 6s; /* 总时长6秒，包含上下运动和停留时间 */
  animation-delay: var(--animation-delay, 0s);
  animation-iteration-count: infinite; /* 无限循环 */
  animation-direction: normal;
  animation-fill-mode: both;
}

@keyframes floatUpDown {
  0%, 25% {
    transform: translateY(0); /* 起始位置停留1.5秒 (25%的6秒=1.5秒) */
  }
  45% {
    transform: translateY(calc(-1 * var(--float-amplitude, 5px))); /* 上升到顶点 */
  }
  50%, 75% {
    transform: translateY(calc(-1 * var(--float-amplitude, 5px))); /* 顶点停留1.5秒 */
  }
  95% {
    transform: translateY(0); /* 下降回原位 */
  }
  100% {
    transform: translateY(0); /* 完成一个周期 */
  }
}

@keyframes floatDownUp {
  0%, 25% {
    transform: translateY(0); /* 起始位置停留1.5秒 */
  }
  45% {
    transform: translateY(var(--float-amplitude, 5px)); /* 下降到底点 */
  }
  50%, 75% {
    transform: translateY(var(--float-amplitude, 5px)); /* 底点停留1.5秒 */
  }
  95% {
    transform: translateY(0); /* 上升回原位 */
  }
  100% {
    transform: translateY(0); /* 完成一个周期 */
  }
}
</style> 