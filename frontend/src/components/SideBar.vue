<template>
  <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
    <div class="toggle-btn" @click="toggleCollapse">
      <el-icon v-if="isCollapsed"><ArrowRight /></el-icon>
      <el-icon v-else><ArrowLeft /></el-icon>
    </div>
    
    <div class="menu-container" v-show="!isCollapsed">
      <div 
        v-for="(item, index) in items" 
        :key="index"
        class="menu-item"
        :class="{ 'active': activeMenuItem === item.key }"
        @click="selectMenuItem(item)"
      >
        <div class="menu-icon">
          <img v-if="item.icon" :src="item.icon" alt="" class="icon-img" />
          <span v-else class="icon-letter">P</span>
        </div>
        <span class="menu-text">{{ item.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import { ArrowRight, ArrowLeft } from '@element-plus/icons-vue';

interface MenuItem {
  key: string;
  title: string;
  component?: string;
  icon?: string;
}

const props = defineProps<{
  parentKey: string;
  parentTitle: string;
  items: MenuItem[];
  defaultActive?: string;
}>();

const emit = defineEmits(['select', 'collapse-change']);

// 控制侧边栏折叠状态
const isCollapsed = ref(true);

// 当前激活的菜单项
const activeMenuItem = ref('');

// 折叠/展开侧边栏
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
  emit('collapse-change', isCollapsed.value);
};

// 选择菜单项
const selectMenuItem = (item: MenuItem) => {
  activeMenuItem.value = item.key;
  emit('select', item);
};

// 初始化默认选中项
onMounted(() => {
  if (props.defaultActive && props.items.some(item => item.key === props.defaultActive)) {
    activeMenuItem.value = props.defaultActive;
  } else if (props.items.length > 0) {
    activeMenuItem.value = props.items[0].key;
  }
});

// 监听props变化，更新默认选中项
watch(() => props.defaultActive, (newVal) => {
  if (newVal && props.items.some(item => item.key === newVal)) {
    activeMenuItem.value = newVal;
  }
});

// 监听parentKey变化，重新设置默认菜单项
watch(() => props.parentKey, () => {
  if (props.defaultActive && props.items.some(item => item.key === props.defaultActive)) {
    activeMenuItem.value = props.defaultActive;
  } else if (props.items.length > 0) {
    activeMenuItem.value = props.items[0].key;
  }
});
</script>

<style scoped>
.sidebar {
  height: 100%;
  width: 200px;
  background: linear-gradient(to bottom, #e0e8fa, #f6efff);
  transition: all 0.3s ease;
  position: relative;
  border-right: 1px solid #e0e0e0;
  overflow: hidden;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

.sidebar.collapsed {
  width: 35px;
}

.toggle-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
  background-color: #ffffff;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  color: #547bf5;
}

.menu-container {
  padding: 33px 0;
  height: 100%;
  overflow-y: auto;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  height: 40px;
  margin-left: 10px;
  margin-right: 10px;
  border-radius: 10px;
}

.menu-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.icon-letter {
  color: white;
  font-weight: bold;
  font-size: 16px;
  font-family: Arial, sans-serif;
}

.icon-img {
  width: 20px;
  height: 20px;
}

.menu-text {
  flex: 1;
  color: #333;
}

.menu-item:hover {
  background-color: rgba(232, 233, 235, 0.5);
}

.menu-item.active {
  background-color: #ffffff;
  border-left: none;
}

.menu-item.active .menu-text {
  color: #4e73ef;
  font-weight: 500;
}
</style> 