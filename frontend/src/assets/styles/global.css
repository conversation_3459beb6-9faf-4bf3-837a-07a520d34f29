/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
}

body {
  background-color: transparent;
  color: #fff;
  min-height: 100vh;
  margin: 0;
  overflow: hidden;
}

#app {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 0;
  width: 100%;
  overflow: hidden;
  height: auto;
  min-height: calc(100vh - 70px); /* 减去header高度 */
}

/* 模块通用样式 */
.module-container {
  margin-bottom: 20px;
  border-radius: 6px;
  overflow: hidden;
  width: 100%;
  height: auto;
  min-height: 100px;
}

.module-header {
  display: flex;
  align-items: center;
  height: 42px;
  /* padding: 0 15px; */
  background: linear-gradient(to right, rgba(45, 92, 196, 0.9), rgba(14, 46, 94, 0.9));
  border-radius: 20px 20px 20px 20px;
  font-size: 16px;
  font-weight: bold;
  color: rgb(187, 255, 254);
  border: 1px solid #00fffc;
}

.module-content {
  background-color: rgba(21, 63, 105, 0.5);
  padding: 15px;
  border-radius: 20px 20px 20px 20px;
  height: auto;
  min-height: 110px;
}

/* 数据展示卡片 */
.data-card {
  background: rgba(14, 47, 106, 0.5);
  border-radius: 6px;
  padding: 15px;
  transition: all 0.3s;
}

.data-card:hover {
  background: rgba(14, 47, 106, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 数字样式 */
.number {
  font-family: 'Arial', sans-serif;
  font-weight: bold;
  color: #00fffc;
}

/* 大标题样式 */
.main-title {
  text-align: center;
  padding: 10px 0;
  font-size: 36px;
  color: rgb(50, 243, 245);
  text-shadow: 0 0 10px rgba(50, 243, 245, 0.6);
  background: linear-gradient(to top, rgba(14, 47, 106, 0.8), rgba(0, 12, 47, 0));
  border-radius: 50%;
  margin: 0 auto 20px;
  box-shadow: 0 5px 10px rgba(0, 100, 255, 0.4);
  border: none;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  position: relative;
  padding-top: 2px;
  padding-bottom: 18px;
}

/* 标题文字样式 */
.main-title-text {
  position: relative;
  z-index: 1;
}

/* 标题装饰椭圆 */
.main-title-decorator {
  position: absolute;
  width: 82.875%;
  height: 53.675%;
  bottom: 0;
  left: 8.5625%;
  background: linear-gradient(to top, rgb(58, 126, 240), rgb(4, 21, 63));
  border-radius: 50%;
  z-index: 0;
}

/* 响应式布局 */
.grid-layout {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr;
  gap: 20px;
  padding: 0 20px;
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  height: auto;
  min-height: calc(100vh - 120px);
}

@media (max-width: 1600px) {
  .grid-layout {
    padding: 0 15px;
  }
}

@media (max-width: 1200px) {
  .grid-layout {
    grid-template-columns: 1fr 1fr;
    height: auto;
  }
  
  .right-column {
    grid-column: span 2;
  }
  
  .middle-column {
    display: none !important;
  }
}

@media (max-width: 768px) {
  .grid-layout {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 10px;
  }
  
  .right-column {
    grid-column: span 1;
  }
} 