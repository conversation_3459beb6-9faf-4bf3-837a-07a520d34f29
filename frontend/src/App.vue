<script setup lang="ts">
import Header from './components/Header.vue'
import DynamicBackground from './components/DynamicBackground.vue'
</script>

<template>
  <div id="app">
    <template v-if="$route.path !== '/login'">
      <DynamicBackground />
      <Header />
    </template>
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<style>
@import './assets/styles/global.css';

#app {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative; /* 确保子元素的绝对定位是相对于#app */
}

.main-content {
  flex: 1;
  width: 100%;
  overflow: hidden;
  position: relative; /* 确保z-index生效 */
  z-index: 1; /* 确保内容在背景之上 */
}
</style>
