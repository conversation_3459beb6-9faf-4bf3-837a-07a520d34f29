import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/risk-management'
  },
  {
    path: '/risk-management',
    name: 'RiskManagement',
    component: () => import('../views/RiskManagement.vue'),
    meta: {
      title: '信访风险源头治理'
    }
  },
  {
    path: '/risk-identification',
    name: 'RiskIdentification',
    component: () => import('../views/RiskIdentification.vue'),
    meta: {
      title: '信访案件风险识别与防控'
    }
  },
  {
    path: '/source-identification',
    name: 'SourceIdentification',
    component: () => import('../views/SourceIdentification.vue'),
    meta: {
      title: '信访风险源头识别与防控'
    }
  },
  {
    path: '/visitor-portrait',
    name: 'VisitorPortrait',
    component: () => import('../views/VisitorPortrait.vue'),
    meta: {
      title: '来访人员画像'
    }
  },
  {
    path: '/case-detail/:bmsah',
    name: 'CaseDetail',
    component: () => import('../views/CaseDetail.vue'),
    meta: {
      title: '案件详情'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/sync-task-manager',
    name: 'SyncTaskManager',
    component: () => import('../views/SyncTaskManager.vue'),
    meta: {
      title: '同步任务管理'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 