{"name": "xfajfxsbyfk", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.11", "marked": "^15.0.12", "pdfjs-dist": "^4.10.38", "pinia": "^3.0.2", "sse.js": "^2.6.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.21", "@vitejs/plugin-vue": "^5.2.3", "typescript": "^5.8.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}