/**
 * @projectName wstools
 * @package com.thunisoft.wstools.sftp
 * @className com.thunisoft.wstools.sftp.SftpUtil
 * @copyright Copyright 2022 Thunisoft, Inc All rights reserved.
 */
package com.smxz.dakongshen.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;

/**
 * SftpUtil
 * @description
 * <AUTHOR>
 * @date 2022/12/4 14:43
 * @version TODO
 */
@Slf4j
@Component
public class SftpUtil {

    public InputStream download(ChannelSftp obj, String wjlj) {
        wjlj = wjlj.replaceAll("\\\\", "/");
        InputStream sftpStream = null;
        try {
            if (getObj(wjlj, obj)) {
                // 1. 读取文件到内存
                sftpStream = obj.get(FilenameUtils.getName(wjlj));
                byte[] fileData = sftpStream.readAllBytes();
                return new ByteArrayInputStream(fileData);
            }
        } catch (Exception e) {
            log.error("通过Sftp 获取文件发生异常，文件路径为{}",wjlj, e);
        } finally {
            IOUtils.closeQuietly(sftpStream);
        }
        return null;
    }

    private boolean getObj(String path, ChannelSftp obj) throws Exception {
        String realPath = FilenameUtils.separatorsToUnix(path);
        if (StringUtils.isNotBlank(realPath)) {
            obj.cd(FilenameUtils.getPath(realPath));
        }
        return true;
    }

    /**
     * 将输入流的数据上传到sftp作为文件。文件完整路径=basePath+directory
     *
     * @param sftp     上传到该目录
     * @param wjlj
     * @param input    输入流
     */
    public void upload(ChannelSftp sftp, String wjlj, InputStream input) throws SftpException {
        try {
            wjlj = wjlj.replaceAll("\\\\", "/");
            sftp.cd(FilenameUtils.getPath(wjlj));
        } catch (SftpException e) {
            //目录不存在，则创建文件夹
            String[] dirs = FilenameUtils.getPath(wjlj).split("/");
            for (String dir : dirs) {
                if (null == dir || "".equals(dir)) {
                    continue;
                }
                try {
                    sftp.cd(dir);
                } catch (SftpException ex) {
                    sftp.mkdir(dir);
                    sftp.cd(dir);
                }
            }
        }
        //上传文件
        sftp.put(input, FilenameUtils.getName(wjlj));
    }

}