package com.smxz.dakongshen.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.pastdev.jsch.Slf4jBridge;
import com.smxz.dakongshen.config.ClientPoolConfig;
import com.smxz.dakongshen.config.SftpProperties;
import com.smxz.dakongshen.entity.BaseProperties;
import jakarta.annotation.PostConstruct;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.security.Security;

/**
 * {@code FtpClient} 连接池管理类，用来初始化和销毁 {@code SFTP} 连接池，以及对外提供获取和释放 {@code SFTP} 连接.
 *
 * @since v1.0.0
 */
@Slf4j
@Component
public class SftpClientPoolManager {


    @Autowired
    ClientPoolConfig poolConfig;

    /**
     * FTP 连接池.
     */
    @Setter
    private SftpClientPool pool;

    /**
     * 构造方法.
     */
    public SftpClientPoolManager() {

    }

    /**
     * 初始化 SFTP 连接池对象实例.
     */
    @PostConstruct
    public   void  init() throws Exception {
        if (this.pool==null){
            //兼容Centos8  麒麟环境
            Security.insertProviderAt(new BouncyCastleProvider(), 1);
            JSch.setConfig("kex", JSch.getConfig("kex") + ",curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group-exchange-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group14-sha256");
            JSch.setConfig("server_host_key", JSch.getConfig("server_host_key") + ",<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,ssh-ed25519,rsa-sha2-512,rsa-sha2-256,ssh-rsa,ssh-dss");
            JSch.setLogger(new Slf4jBridge());
            //kerberos  禁止认证
            JSch.setConfig("PreferredAuthentications", JSch.getConfig("PreferredAuthentications") + ",publickey,keyboard-interactive,password");
            this.pool=new SftpClientPool(new SftpClientFactory(),poolConfig.buildSftpPoolConfig());
        }
    }


    /**
     * 获取 SFTP 客户端连接对象实例.
     *
     * @param info SFTP 信息
     * @return SFTP 客户端连接扩展实例
     * @throws Exception 异常
     */
    public  ChannelSftp getSftpClient(BaseProperties info) throws Exception {
        final long startTime = System.currentTimeMillis();
        ChannelSftp channelSftp;
        try {
            channelSftp= pool.borrowObject(info);
            String defualtDir = channelSftp.getHome();
            log.info("【 SFTPPool 提示】从 SFTP 连接池获取连接【{}】成功,默认路径【{}】,用时：【{}】毫秒，"
                            + "池中可用连接数量：【{}】个，活跃连接数量为：【{}】个。", info.getHost(), defualtDir,
                    (System.currentTimeMillis() - startTime), pool.getNumIdle(), pool.getNumActive());
            return channelSftp;
        } catch (Exception e) {
            log.error("【 SFTPPool 警示】 getSftpClient 方式执行异常，将直接返回 SFTP Client 对象【{}】",info, e);
            return null;
        }
    }

    /**
     * 释放 SFTP 客户端对象的连接，此处不需要恢复 SFTP 的工作路径，钝化连接的时候进行路径还原.
     *
     * @param info 连接信息
     * @param client SFTP
     */
    public void releaseFtpClient(BaseProperties info, ChannelSftp client) {
        try {
            if(client!=null){
                pool.returnObject(info,client);
            }
        } catch (Exception e) {
            log.error("releaseFtpClient报错",e);
            throw new RuntimeException(e);
        }
        log.debug("【SFTPPool 提示】释放了当前 SFTP 连接成功。SFTP 连接地址:【{}】, 默认路径:【{}】, 池中可用连接数量:【{}】个, "
                        + "总数量为:【{}】个.", info, info.getWorkDirectory(),
                pool.getNumIdle(), (pool.getNumActive() + pool.getNumIdle()));
    }
}
