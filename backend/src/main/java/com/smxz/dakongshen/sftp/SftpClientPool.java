/**
 * @projectName wstools
 * @package com.thunisoft.wstools.sftp
 * @className com.thunisoft.wstools.sftp.SftpClientPool
 * @copyright Copyright 2022 Thunisoft, Inc All rights reserved.
 */
package com.smxz.dakongshen.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.smxz.dakongshen.entity.BaseProperties;
import org.apache.commons.pool2.KeyedPooledObjectFactory;
import org.apache.commons.pool2.impl.GenericKeyedObjectPool;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;

/**
 * SftpClientPool
 * @description
 * <AUTHOR>
 * @date 2022/12/5 19:50
 * @version TODO
 */
public class SftpClientPool  extends GenericKeyedObjectPool<BaseProperties, ChannelSftp> {
    /**
     * 构造方法.
     *
     * @param factory 工厂
     * @param config 配置类
     */
    public SftpClientPool(KeyedPooledObjectFactory<BaseProperties, ChannelSftp> factory,
                         GenericKeyedObjectPoolConfig<ChannelSftp> config) {
        super(factory, config);
    }

    /**
     * 构造方法.
     *
     * @param factory 工厂
     */
    public SftpClientPool(KeyedPooledObjectFactory<BaseProperties, ChannelSftp> factory) {
        this(factory, new GenericKeyedObjectPoolConfig<>());
    }
}