/**
 * @projectName wstools
 * @package com.thunisoft.wstools.ftp
 * @className com.thunisoft.wstools.ftp.FTPClientFactory
 * @copyright Copyright 2020 Thunisoft, Inc All rights reserved.
 */
package com.smxz.dakongshen.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.smxz.dakongshen.entity.BaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BaseKeyedPooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;


/**
 * FTPClientFactory
 * @description
 * <AUTHOR>
 * @date 2022/3/11 11:00
 * @version 2.1
 */
@Slf4j()
public class SftpClientFactory extends BaseKeyedPooledObjectFactory<BaseProperties,ChannelSftp> {

    private static final int CONNECT_TIME_OUT=120*1000;


    public SftpClientFactory() {
    }
    @Override
    public ChannelSftp create(BaseProperties config) throws Exception {
        JSch jsch = new JSch();
        Session session = jsch.getSession(config.getUsername(),config.getHost());
        session.setPort(config.getPort());
        session.setPassword(config.getPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect(CONNECT_TIME_OUT);
        ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect(CONNECT_TIME_OUT);
        return channelSftp;
    }

    @Override
    public PooledObject<ChannelSftp> wrap(ChannelSftp channelSftp) {
        return new DefaultPooledObject<>(channelSftp);
    }

    @Override
    public PooledObject<ChannelSftp> makeObject(BaseProperties key) throws Exception {
        return super.makeObject(key);
    }

    @Override
    public void destroyObject(BaseProperties key, PooledObject<ChannelSftp> p) throws Exception {
        closeConnect(p.getObject());
    }

    @Override
    public boolean validateObject(BaseProperties key, PooledObject<ChannelSftp> p) {
        try {
         return p.getObject().isConnected();
        } catch (Exception e) {
            log.warn("【SFTPPool 错误】校验SFTP 连接【{}】出错，错误信息为:【{}】。", key, e.getMessage());
            return false;
        }
    }

    @Override
    public void activateObject(BaseProperties key, PooledObject<ChannelSftp> p) throws Exception {
        p.getObject().cd(key.getWorkDirectory());
    }

    @Override
    public void passivateObject(BaseProperties key, PooledObject<ChannelSftp> p) throws Exception {
        p.getObject().cd(key.getWorkDirectory());
    }

    private void closeConnect(ChannelSftp sftp) {
        if (sftp.isConnected()) {
            sftp.disconnect();
        }
    }
}
