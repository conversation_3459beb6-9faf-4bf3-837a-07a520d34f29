package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petitioner_tag")
public class PetitionerTag {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long petitionerId;

    private String content;

    private String level;

    private LocalDateTime createTime;
}
