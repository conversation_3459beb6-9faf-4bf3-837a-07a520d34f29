package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petitioner_portrait")
public class PetitionerPortrait {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String petitionerName;

    private String riskLevel;

    private String tagSummary;

    private String petitionHistorySummary;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
