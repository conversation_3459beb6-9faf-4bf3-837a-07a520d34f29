/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.entity.tyyw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-05-28 14:56
 **/
@TableName(value = "TYYW2_LCBA.JCNJ_YX_AJJZWJ",autoResultMap = true)
@Data
@Builder
public class Ajjzwj {

    /**
     * 部门受案号
     */
    @TableId
    private String yxslbh;
    /**
     * 文件名称
     */
    private String wjmc;

    /**
     * 是否删除
     */
    private String sfsc;

    /**
     *  转版前文件存放路径
     */
    private String zbqwjcflj;

    /**
     * 转版前文件存放名称
     */
    private String zbqfwqwjmc;
    /**
     * 文书模板编号
     */
    private String wsmbbh;
    /**
     * 文件扩展名
     */
    private String wjkzm;

    /**
     * 最后更新时间
     */
    private LocalDateTime zhxgsj;



}
