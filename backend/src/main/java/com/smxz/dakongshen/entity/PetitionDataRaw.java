package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@TableName("petition_data_raw")
@Getter
@Setter
public class PetitionDataRaw {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "信访次数")
    private String petitionCount;

    @Schema(title = "信访时间")
    private String petitionTime;

    @Schema(title = "承办单位")
    private String organizer;

    @Schema(title = "登记人")
    private String registrant;

    @Schema(title = "案件名称")
    private String caseName;

    @Schema(title = "信访类型")
    private String petitionType;

    @Schema(title = "重复信访")
    private String repeatedPetition;

    @Schema(title = "重复信访治理")
    private String repeatedPetitionGovernance;

    @Schema(title = "信访人姓名")
    private String petitionerName;

    @Schema(title = "案发地")
    private String petitionLocation;

    @Schema(title = "信访渠道")
    private String petitionChannel;

    @Schema(title = "信访方式")
    private String petitionMethod;

    @Schema(title = "案件来源")
    private String caseSource;

    @Schema(title = "涉案领域")
    private String involvementField;

    @Schema(title = "涉检访类型")
    private String sjflx;

    @Schema(title = "接收单位")
    private String receivingOrganization ;

    @Schema(title = "分流/办理情况")
    private String processingStatus;

    @Schema(title = "承办人")
    private String contractor;

    @Schema(title = "案情摘要")
    private String brief;

    @Schema(title = "条码")
    private String barCode;

    @Schema(title = "被信访人姓名")
    private String respondentName;

    @Schema(title = "部门受案号")
    private String departmentAcceptanceNumber;

    @Schema(title = "所属导入记录的id")
    private Long importId;

}
