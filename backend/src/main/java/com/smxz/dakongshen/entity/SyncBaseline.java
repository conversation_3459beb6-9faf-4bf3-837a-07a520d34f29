/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-10 11:11
 **/
@Data
@TableName("sync_baseline")
@Builder
public class SyncBaseline {

    /**
     * 主键ID（自增）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    private LocalDateTime lastExecuteTime;

    private LocalDateTime executeBeginTime;

    private LocalDateTime executeFinishTime;



}
