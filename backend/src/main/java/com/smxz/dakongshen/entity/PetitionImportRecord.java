package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petition_import_record")
public class PetitionImportRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String importType;

    private String importStatus;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
