package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("ashen_assistant")
public class AshenAssistant {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String chatId;

    private LocalDateTime createTime;


}
