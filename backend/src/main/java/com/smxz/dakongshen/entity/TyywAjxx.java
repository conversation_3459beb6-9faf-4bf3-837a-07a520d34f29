/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.smxz.ragflow.model.session.RagAnswer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-05 09:57
 **/
@Data
@TableName(value = "tyyw_ajxx", autoResultMap = true)
public class TyywAjxx {

    @TableId
    private String bmsah;

    private String tysah;

    private String cbdwBm;

    private String cbdwMc;

    private String ajmc;

    private String aydm;

    private String aymc;

    private LocalDate slrq;

    private String cbbmMc;

    private String cbjcg;

    private String aqzy;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<TyywXyr> xyrList;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private RagAnswer answer;

    private String fxsbqk;

    private String wjmc;

    private String pdfObject;

    @TableField(typeHandler = Fastjson2TypeHandler.class)
    public List<Tag> tags;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Tag{
        /**
         * 标签名
         */
        private String tag;
        /**
         * 原文内容
         */
        private String text;
    }

}
