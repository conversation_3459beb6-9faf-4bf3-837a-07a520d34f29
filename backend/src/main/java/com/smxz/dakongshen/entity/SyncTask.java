/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 诚
 * @description 同步任务实体
 * @since 2025-05-29 11:00
 **/
@TableName("sync_task")
@Builder
@Data
@Accessors(chain = true)
public class SyncTask {

    /**
     * 主键ID（自增）
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务起始日期（非空）
     * 格式：yyyy-MM-dd
     */
    private LocalDate startDate;

    /**
     * 任务结束日期（非空）
     * 格式：yyyy-MM-dd
     */
    private LocalDate endDate;

    /**
     * 任务状态
     * 0-待处理（默认值）
     * 1-处理中
     * 2-已完成
     */
    private Integer status;

    /**
     * 总记录数（默认0）
     */
    private Integer totalCount ;

    /**
     * 成功记录数（默认0）
     */
    private Integer successCount ;

    /**
     * 任务开始时间（可为空）
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime beginTime;

    /**
     * 任务完成时间（可为空）
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime finishTime;


}
