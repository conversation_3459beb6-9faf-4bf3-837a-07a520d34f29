package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("petition_petitioner")
public class PetitionPetitioner {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long petitionId;

    private Long petitionerId;

    private LocalDateTime createTime;

}
