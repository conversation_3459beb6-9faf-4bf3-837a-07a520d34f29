package com.smxz.dakongshen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@TableName("case_risk_identification_history")
public class CaseRiskIdentificationHistory {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String searchContent;

    private LocalDateTime createTime;

}
