package com.smxz.dakongshen.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smxz.commons.exception.BusinessException;
import com.smxz.dakongshen.dto.AiHistoryPetitionInput;
import com.smxz.dakongshen.dto.AiTagsAndSummary;
import com.smxz.dakongshen.dto.request.ExcelImportData;
import com.smxz.dakongshen.entity.*;
import com.smxz.dakongshen.mapper.*;
import com.smxz.dakongshen.util.PetitionRecordListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class PetitionRecordService {

    @Autowired
    private PetitionDataRawMapper petitionDataRawMapper;

    @Autowired
    private PetitionMapper petitionMapper;

    @Autowired
    private PetitionPetitionerMapper petitionPetitionerMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private PetitionImportRecordMapper petitionImportRecordMapper;

    @Autowired
    private PetitionerPortraitMapper petitionerPortraitMapper;

    @Autowired
    private PetitionerTagMapper petitionerTagMapper;

    @Autowired
    private ChatModel chatModel;

    private final static RetryTemplate retryTemplate = RetryTemplate.builder()
            .maxAttempts(10)
            .exponentialBackoff(5000, 2, 60_000)
            .build();

    // 一个线程的线程池，让多次的导入强制排队
    private final static ExecutorService executorService = Executors.newSingleThreadExecutor();

    public void importExcel(MultipartFile file) {
        PetitionImportRecord petitionImportRecord = new PetitionImportRecord();
        petitionImportRecord.setImportType("ExcelImport");
        petitionImportRecord.setImportStatus("running");
        petitionImportRecord.setCreateTime(LocalDateTime.now());
        petitionImportRecordMapper.insert(petitionImportRecord);

        PetitionRecordListener demeDataListener = new PetitionRecordListener(petitionDataRawMapper, petitionImportRecord.getId());
        try (InputStream is = file.getInputStream()) {
            EasyExcel.read(is, ExcelImportData.class, demeDataListener).doReadAll();
        } catch (IOException e) {
            throw new BusinessException("excel解析失败！");
        }
        executePetitionerPortrait(petitionImportRecord.getId());
    }

    public void executePetitionerPortrait(Long importId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        executorService.execute(() -> {
            log.info("信访人物画像分析开始");
            LocalDateTime now = LocalDateTime.now();
            PetitionImportRecord petitionImportRecord = new PetitionImportRecord();
            petitionImportRecord.setId(importId);

            try {
                Map<String, PetitionerPortrait> petitionerPortraitMap = getPetitionerPortrait(importId, now);
                // 针对所有人的画像进行逐个分析
                analyse(petitionerPortraitMap, now);
                petitionImportRecord.setImportStatus("finished");
            } catch (Exception e) {
                petitionImportRecord.setImportStatus("error");
                log.error("数据分析出错", e);
            }
            petitionImportRecord.setCreateTime(LocalDateTime.now());
            petitionImportRecordMapper.updateById(petitionImportRecord);
            stopWatch.stop();
            log.info("信访人物画像分析结束，耗时：{}ms", stopWatch.getTotalTimeMillis());
        });
    }

    private void analyse(Map<String, PetitionerPortrait> petitionerPortraitMap, LocalDateTime now) {
        for (var e : petitionerPortraitMap.values()) {
            List<Petition> historyPetitions = petitionMapper.listPetitionByPetitionerName(e.getPetitionerName());
            // 去掉信访次数
            historyPetitions.forEach(elem -> elem.setPetitionCount(null));
            AiHistoryPetitionInput aiHistoryPetitionInput = new AiHistoryPetitionInput();
            aiHistoryPetitionInput.setName(e.getPetitionerName());
            aiHistoryPetitionInput.setHistoryPetitions(historyPetitions);

            List<Message> messages = new ArrayList<>();
            messages.add(new SystemMessage(SYSTEM_MESSAGE));
            messages.add(new UserMessage("以下是该信访人名称与涉及的历史案件：" + JSON.toJSONString(aiHistoryPetitionInput)));

            log.debug("ai输入：{}", SYSTEM_MESSAGE);
            StringBuilder aiOutput = new StringBuilder();

            List<PetitionerTag> tags = new ArrayList<>();
            retryTemplate.execute(retryContext -> {
                aiOutput.setLength(0);
                doOnAi(now, e, aiOutput, messages, tags);
                return true;
            });

            log.debug("ai输出：{}", aiOutput);

            e.setUpdateTime(now);
            transactionTemplate.execute(status -> {
                petitionerTagMapper.delete(Wrappers.<PetitionerTag>lambdaQuery().eq(PetitionerTag::getPetitionerId, e.getId()));
                petitionerTagMapper.insert(tags);
                petitionerPortraitMapper.updateById(e);

                return true;
            });
        }
    }

    private void doOnAi(LocalDateTime now, PetitionerPortrait petitionerPortrait, StringBuilder aiOutput,
                            List<Message> messages, List<PetitionerTag> tags) {
        
        chatModel.stream(new Prompt(messages))
                .flatMap(chatResponse -> {
                    List<Generation> results = chatResponse.getResults();
                    return results != null ? Flux.fromIterable(results) : Flux.empty();
                })
                .map(content -> ObjectUtils.defaultIfNull(content.getOutput().getText(), ""))
                .doOnNext(aiOutput::append)
                .blockLast(); // 阻塞直到流结束

        // 去掉一些内容
        String response = aiOutput.toString();
        int lastIndex = response.lastIndexOf("</think>");
        if (lastIndex != -1) {
            response = response.substring(lastIndex + "</think>".length());
        }
        response = response.replace("```json", "");
        response = response.replace("```", "");
        try {
            AiTagsAndSummary aiTagsAndSummary = JSON.parseObject(response, AiTagsAndSummary.class);

            petitionerPortrait.setTagSummary(aiTagsAndSummary.getTagSummary());
            petitionerPortrait.setPetitionHistorySummary(aiTagsAndSummary.getPetitionHistorySummary());
            if (aiTagsAndSummary.getTags() != null) {
                tags.addAll(aiTagsAndSummary.getTags());
            }
            petitionerPortrait.setRiskLevel(getRishLevel(tags.size()));
            tags.forEach(tag -> {
                tag.setPetitionerId(petitionerPortrait.getId());
                tag.setCreateTime(now);
            });
        } catch (Exception ex) {
            log.warn("ai输出内容解析失败，ai输出：{}", aiOutput);
            throw ex;
        }
    }

    @NotNull
    private Map<String, PetitionerPortrait> getPetitionerPortrait(Long importId, LocalDateTime now) {
        // 拿出整批数据
        // 暂时全在内存操作
        LambdaQueryWrapper<PetitionDataRaw> wrapper = Wrappers.<PetitionDataRaw>lambdaQuery()
                .eq(PetitionDataRaw::getImportId, importId);
        List<PetitionDataRaw> petitionDataRawList = petitionDataRawMapper.selectList(wrapper);

        // 转换一下结构
        List<Petition> petitions = petitionDataRawList.stream()
                .map(e -> {
                    Petition r = new Petition();
                    BeanUtils.copyProperties(e, r);
                    r.setRawId(e.getId());
                    r.setPetitionTime(convertToLocalDateTime(e.getPetitionTime()));

                    return r;
                }).toList();

        petitionMapper.insert(petitions);
        log.debug("插入信访案件信息完成");

        // 把本次涉及的所有人的姓名拿到
        List<String> names = petitions.stream()
                .flatMap(e -> {
                    String petitionerName = e.getPetitionerName().replaceFirst("等\\d*人$", "");
                    String[] petitionersArray = petitionerName.split("、");

                    return Arrays.stream(petitionersArray).map(String::trim);
                })
                .distinct()
                .toList();

        // 查询信访人画像，看看是否有画像
        LambdaQueryWrapper<PetitionerPortrait> portraitQueryWrapper = Wrappers.<PetitionerPortrait>lambdaQuery()
                .in(PetitionerPortrait::getPetitionerName, names);
        Map<String, PetitionerPortrait> existPortraitMap = petitionerPortraitMapper.selectList(portraitQueryWrapper).stream()
                .collect(Collectors.toMap(PetitionerPortrait::getPetitionerName, Function.identity()));


        List<PetitionerPortrait> addedPetitionerPortrait = names.stream()
                .filter(e -> !existPortraitMap.containsKey(e))
                .map(e -> {
                    PetitionerPortrait r = new PetitionerPortrait();
                    r.setPetitionerName(e);
                    r.setCreateTime(now);
                    r.setUpdateTime(now);
                    return r;
                })
                .toList();

        // 将新增的数据插入回去
        petitionerPortraitMapper.insert(addedPetitionerPortrait);
        log.debug("新增信访人画像：{}条", addedPetitionerPortrait.size());
        Map<String, PetitionerPortrait> petitionerPortraitMap = Stream.concat(addedPetitionerPortrait.stream(), existPortraitMap.values().stream())
                .collect(Collectors.toMap(PetitionerPortrait::getPetitionerName, Function.identity()));

        List<PetitionPetitioner> petitionPetitioners = petitions.stream()
                .flatMap(e -> {
                    String petitionerName = e.getPetitionerName().replaceFirst("等\\d*人$", "");
                    String[] petitionersArray = petitionerName.split("、");
                    return Arrays.stream(petitionersArray)
                            .map(s -> {
                                PetitionerPortrait portrait = petitionerPortraitMap.get(s);
                                PetitionPetitioner r = new PetitionPetitioner();
                                r.setPetitionId(e.getId());
                                r.setPetitionerId(portrait.getId());
                                r.setCreateTime(now);

                                return r;
                            });
                }).toList();

        petitionPetitionerMapper.insert(petitionPetitioners);
        log.debug("新增信访人与信访案件关联关系：{}条", petitionPetitioners.size());
        return petitionerPortraitMap;
    }

    private String getRishLevel(int tagSize) {
        if (tagSize >= 5) {
            return "high";
        } else if (tagSize >= 3) {
            return "medium";
        } else {
            return "low";
        }
    }

    private final static DateTimeFormatter FORMATTER_1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private LocalDateTime convertToLocalDateTime(String dateTimeStr) {
        try {
            // 先尝试带时间的格式解析
            return LocalDateTime.parse(dateTimeStr, FORMATTER_1);
        } catch (DateTimeParseException e1) {
            try {
                // 若失败则尝试不带时间的格式，并设置时间为00:00:00
                return LocalDateTime.parse(dateTimeStr + " 00:00:00", FORMATTER_1);
            } catch (DateTimeParseException e2) {
                log.warn("无法解析的日期格式: {}", dateTimeStr);
                return null;
            }
        }
    }

    private static final String SYSTEM_MESSAGE = """
            你是一名拥有20年工作经验的检察官，见识过各种情况，现在面对一个来访人员过往的信访历史材料，你需要给他标记最少0，最多8个风险类别（标签），最后给他形成历史来访总结、风险类别总结。
            风险类别规则：
            1. 缠闹访：简要案情部分出现“缠闹”“缠访”“闹访”“滞留”“静坐”“辱骂”“扰乱秩序”“管制刀具”“老户”“上访老户”“老信访户”字样；来访时间超过4个小时；在一次信访事项办结前来访超过3次；在信访现场出现辱骂他人、静坐、或者携带管制刀具等危险物品到场的情形。
            2. 集体访：简要案情部分出现“集体”“集体访”“群体访”“大规模”；来访人员超过5人。
            3. 越级访：简要案情部分出现“到省”“赴省”“赴京”“进京”“上京”“到北京”“去北京”“越级”“越级上访”“中南海”“天安门”；跨越本级机关向上级机关信访。
            4. 扬言极端访：简要案情部分出现“扬言”“极端”“自杀”“自尽”“自残”“跳楼”“驾车冲撞”“砍杀”“枪击”“开枪”“爆炸”“放火”“投毒”“同归于尽”“血溅三尺”“拿命拼”“拼了”“报复”“偏激”“偏执”“情绪失控”“过激”“三失一偏”；为了报复社会、引起关注、向党委政府及相关政法单位施压等目的扬言实施自杀自残或者驾车冲撞、砍杀、枪击、爆炸、放火、投放危险物质等危害公共安全的极端行为。
            5. 重复访：简要案情部分出现“重复访”“重复信访”“重复来访”“多次来访”“多次来电”“上访老户”“老信访户”；同一信访人就同一信访事项在一定时期内连续两次以上来信来访。
            要求：
            1. 风险类别严格按照风险类别规则输出，如果没有发现规则上的情况，可以不输出风险类别。
            2. 关于riskLevel如果类别数量在[0,3)则为低风险，风险类别数量在[3,5)则为中风险，风险类别数量在[5,+∞)则为高风险
            3. 总结需合结合标签，过往信访情况等进行总结。
            输出要求：
            1. 严格按照JSON格式输出。
            输出格式：
            {
                // tags中的内容就是风险类别
                "tags": [
                    { content: "缠闹访"},
                    { content: "扬言极端访" },
                    { content: "重复访" }
                ]
                "tagSummary": "该来访人涉及了多个风险类别，是{riskLevel}等级！该来访人员是外地的独生子，目前家庭关系破碎，情绪比较激动，存在缠闹访风险。",
                "petitionHistorySummary": "该来访人来访次数3次，分别于2025年1月2号、2025年1月15号、2025年2月2号，来访事项都是为民事申诉案件监督，但是其案件涉及了女民和未成年人，且在多次信访时有缠访的风险，检察官需重点关注。"
            }
            """;

}
