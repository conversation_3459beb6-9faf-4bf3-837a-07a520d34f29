package com.smxz.dakongshen.service;

import com.smxz.dakongshen.dto.request.RiskRequest;
import com.smxz.dakongshen.dto.response.AllSyCount;
import com.smxz.dakongshen.dto.response.CaseAnalysisItem;
import com.smxz.dakongshen.dto.response.NameValueItem;
import com.smxz.dakongshen.dto.response.VisitOverviewResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 信访风险源头治理服务
 */
@Service
public class RiskManagementService {

    @Autowired
    private IPetitionDataRawService petitionDataRawService;

    Pattern dqPattern = Pattern.compile("(?<=市)(.+?)(?=区)");

    /**
     * 获取信访渠道分析数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 信访渠道分析数据
     */
    public List<NameValueItem> getVisitChannelAnalysis(RiskRequest request) {
        
        return petitionDataRawService.countByPetitionChannel(request.getStartDate(), request.getEndDate());
    }
    
    /**
     * 获取信访风险类型分析数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 信访风险类型分析数据
     */
    public List<NameValueItem> getVisitTopTypeAnalysis(RiskRequest request) {

        List<NameValueItem> nameValueItems = petitionDataRawService.countByPetitionMethod(request.getStartDate(), request.getEndDate());
        return nameValueItems;
    }
    
    /**
     * 获取信访事项涉及罪名数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 信访事项涉及罪名数据
     */
    public List<NameValueItem> getCrimeTypeAnalysis(RiskRequest request) {
        // 模拟数据
        List<NameValueItem> result = new ArrayList<>();
        
        NameValueItem item1 = new NameValueItem();
        item1.setName("诈骗罪");
        item1.setValue(100);
        result.add(item1);
        
        NameValueItem item2 = new NameValueItem();
        item2.setName("走私普通货物、物品罪");
        item2.setValue(90);
        result.add(item2);
        
        NameValueItem item3 = new NameValueItem();
        item3.setName("故意伤害罪");
        item3.setValue(85);
        result.add(item3);
        
        NameValueItem item4 = new NameValueItem();
        item4.setName("贪污受贿罪");
        item4.setValue(70);
        result.add(item4);
        
        NameValueItem item5 = new NameValueItem();
        item5.setName("非法吸收公众存款");
        item5.setValue(50);
        result.add(item5);
        
        return result;
    }
    
    /**
     * 获取地图数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 地图数据
     */
    public List<NameValueItem> getMapData(RiskRequest request) {
        List<NameValueItem> nameValueItems = petitionDataRawService.countByPetitionLocation(request.getStartDate(), request.getEndDate());
        nameValueItems.stream().map(item -> {
            String name =  item.getName();
            Matcher matcher = dqPattern.matcher(name);
            if(matcher.find()){
                item.setName(matcher.group(1));
            }
            return item;
        }).collect(Collectors.toUnmodifiableList());
        return nameValueItems;
    }
    
    /**
     * 获取全市信访量和广州市院信访量数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 全市信访量和广州市院信访量数据
     */
    public VisitOverviewResponse getVisitOverview(RiskRequest request) {
        VisitOverviewResponse response = new VisitOverviewResponse();
        AllSyCount allSyCount = petitionDataRawService.countDpByTimeRange(request.getStartDate(), request.getEndDate());
        response.setQsxfl(allSyCount.getCurrentYearAllCount());
        response.setQsxfltb(calculateYearOnYearGrowth(allSyCount.getCurrentYearAllCount(), allSyCount.getLastYearAllCount()));
        response.setGzsyxfl(allSyCount.getCurrentYearSyCount());
        response.setGzsyxfltb(calculateYearOnYearGrowth(allSyCount.getCurrentYearSyCount(),allSyCount.getLastYearSyCount()));
        return response;
    }

    /**
     * 计算同比增长率
     *
     * @param currentCount 当前时期的数量
     * @param lastCount 上一年同期的数量
     * @return 返回同比增长率，如果上一年同期数量为0，则根据当前时期数量决定增长率
     */
    private BigDecimal calculateYearOnYearGrowth(Integer currentCount, Integer lastCount) {
        BigDecimal tb;
        if (lastCount == 0) {
            tb =  currentCount > 0 ? BigDecimal.valueOf(1.0) : BigDecimal.valueOf(0.0); // 防止除以零
        }else{
            tb = BigDecimal.valueOf(currentCount - lastCount).divide(BigDecimal.valueOf(lastCount),1, RoundingMode.DOWN);
        }
        return tb;
    }
    
    /**
     * 获取信访领域分析数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 信访领域分析数据
     */
    public List<NameValueItem> getVisitFieldAnalysis(RiskRequest request) {
        return petitionDataRawService.countByInvolvementField(request.getStartDate(), request.getEndDate());
    }
    
    /**
     * 获取信访人身分析数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 信访人身分析数据
     */
    public List<NameValueItem> getPersonAnalysis(RiskRequest request) {
        // 模拟数据
        List<NameValueItem> result = new ArrayList<>();
        
        NameValueItem item1 = new NameValueItem();
        item1.setName("普通群众");
        item1.setValue(45);
        result.add(item1);
        
        NameValueItem item2 = new NameValueItem();
        item2.setName("特殊人群");
        item2.setValue(18);
        result.add(item2);
        
        NameValueItem item3 = new NameValueItem();
        item3.setName("重复信访人");
        item3.setValue(22);
        result.add(item3);
        
        NameValueItem item4 = new NameValueItem();
        item4.setName("退休人员");
        item4.setValue(15);
        result.add(item4);
        
        NameValueItem item5 = new NameValueItem();
        item5.setName("青年学生");
        item5.setValue(12);
        result.add(item5);
        
        NameValueItem item6 = new NameValueItem();
        item6.setName("妇女儿童");
        item6.setValue(10);
        result.add(item6);
        
        NameValueItem item7 = new NameValueItem();
        item7.setName("老年人");
        item7.setValue(8);
        result.add(item7);
        
        NameValueItem item8 = new NameValueItem();
        item8.setName("残障人士");
        item8.setValue(6);
        result.add(item8);
        
        NameValueItem item9 = new NameValueItem();
        item9.setName("农民工");
        item9.setValue(14);
        result.add(item9);
        
        NameValueItem item10 = new NameValueItem();
        item10.setName("企业职工");
        item10.setValue(16);
        result.add(item10);
        
        return result;
    }
    
    /**
     * 获取涉检情况分析(成案)数据
     *
     * @param request 请求参数，包含开始日期和结束日期
     * @return 涉检情况分析(成案)数据
     */
    public List<CaseAnalysisItem> getCaseAnalysis(RiskRequest request) {
        // 模拟数据
        List<CaseAnalysisItem> result = new ArrayList<>();
        
        CaseAnalysisItem item1 = new CaseAnalysisItem();
        item1.setName("不履行约定");
        item1.setValue(2311);
        item1.setZb(0.3);
        result.add(item1);
        
        CaseAnalysisItem item2 = new CaseAnalysisItem();
        item2.setName("不服裁销案件");
        item2.setValue(2311);
        item2.setZb(0.3);
        result.add(item2);
        
        CaseAnalysisItem item3 = new CaseAnalysisItem();
        item3.setName("不服没有犯罪");
        item3.setValue(2001);
        item3.setZb(0.25);
        result.add(item3);
        
        CaseAnalysisItem item4 = new CaseAnalysisItem();
        item4.setName("不服其他诉讼");
        item4.setValue(10344);
        item4.setZb(0.10);
        result.add(item4);
        
        CaseAnalysisItem item5 = new CaseAnalysisItem();
        item5.setName("其他");
        item5.setValue(502);
        item5.setZb(0.05);
        result.add(item5);
        
        return result;
    }
} 