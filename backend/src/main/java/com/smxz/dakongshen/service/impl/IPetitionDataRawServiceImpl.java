package com.smxz.dakongshen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.dakongshen.dto.response.AllSyCount;
import com.smxz.dakongshen.dto.response.MonthlyCountItem;
import com.smxz.dakongshen.dto.response.NameValueItem;
import com.smxz.dakongshen.entity.PetitionDataRaw;
import com.smxz.dakongshen.mapper.PetitionDataRawMapper;
import com.smxz.dakongshen.service.IPetitionDataRawService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信访原始数据服务实现类
 */
@Service
public class IPetitionDataRawServiceImpl extends ServiceImpl<PetitionDataRawMapper, PetitionDataRaw>
    implements IPetitionDataRawService {

    @Override
    public IPage<PetitionDataRaw> pageQuery(Page<PetitionDataRaw> page, String petitionType, 
                                           String petitionChannel, LocalDateTime startTime, 
                                           LocalDateTime endTime) {
        LambdaQueryWrapper<PetitionDataRaw> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加条件查询
        if (StringUtils.hasText(petitionType)) {
            queryWrapper.eq(PetitionDataRaw::getPetitionType, petitionType);
        }
        
        if (StringUtils.hasText(petitionChannel)) {
            queryWrapper.eq(PetitionDataRaw::getPetitionChannel, petitionChannel);
        }
        
        if (startTime != null) {
            queryWrapper.ge(PetitionDataRaw::getPetitionTime, startTime);
        }
        
        if (endTime != null) {
            queryWrapper.le(PetitionDataRaw::getPetitionTime, endTime);
        }
        
        // 按信访时间降序排列
        queryWrapper.orderByDesc(PetitionDataRaw::getPetitionTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public List<NameValueItem> countByPetitionMethod(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByPetitionMethod(startTime, endTime);
    }

    @Override
    public List<NameValueItem> countByPetitionChannel(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByPetitionChannel(startTime, endTime);
    }

    @Override
    public List<NameValueItem> countByPetitionLocation(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByPetitionLocation(startTime, endTime);
    }

    @Override
    public List<NameValueItem> countByInvolvementField(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByInvolvementField(startTime, endTime);
    }


    @Override
    public AllSyCount countDpByTimeRange(LocalDateTime currentStartTime, LocalDateTime currentEndTime) {
        // 查询当前时间段的信访数量
        return baseMapper.countDpByTimeRange(currentStartTime,currentEndTime);

    }
    
    @Override
    public List<MonthlyCountItem> countByMonth(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByMonth(startTime, endTime);
    }
} 