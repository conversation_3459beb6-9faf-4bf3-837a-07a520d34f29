/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.service.tyyw;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.smxz.dakongshen.entity.AjYxAj;
import com.smxz.dakongshen.entity.TyywAjxx;
import com.smxz.dakongshen.entity.TyywXyr;
import com.smxz.dakongshen.entity.tyyw.Ajjzwj;
import com.smxz.dakongshen.mapper.TyywAjxxMapper;
import com.smxz.dakongshen.mapper.tyyw.SyncTyywAjxxBaseMapper;
import com.smxz.ragflow.model.session.RagAnswer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-05 11:54
 *
 **/

@Service
@Slf4j
public class SyncTyywAjxxBaseService {

    @Autowired
    private SyncTyywAjxxBaseMapper syncTyywAjxxBaseMapper;

    @Autowired
    private TyywAjxxMapper tyywAjxxMapper;

    public void  startSyncAjxx(Ajjzwj ajjzwj, RagAnswer ragAnswer,String pdfMinioObject){
        String bmsah = ajjzwj.getYxslbh();
        AjYxAj ajYxAj = syncTyywAjxxBaseMapper.selectAjYxAj(bmsah);
        String aqzy = syncTyywAjxxBaseMapper.selectAqzyByBmsah(bmsah);

        TyywAjxx ajxx = syncTyywAjxxBaseMapper.selectScdbByBmsah(bmsah);
        if(ObjectUtils.isNotEmpty(ajxx)){
            List<TyywXyr> tyywXyrs = syncTyywAjxxBaseMapper.selectScdbXyrByBmsah(bmsah);
            ajxx.setXyrList(tyywXyrs);
        }else {
            ajxx = syncTyywAjxxBaseMapper.selectScqsByBmsah(bmsah);
            List<TyywXyr> tyywXyrs = syncTyywAjxxBaseMapper.selectScqsXyrByBmsah(bmsah);
            ajxx.setXyrList(tyywXyrs);
        }
        ajxx.setWjmc(ajjzwj.getWjmc());
        ajxx.setPdfObject(pdfMinioObject);
        ajxx.setAqzy(aqzy);
        if(ObjectUtils.isNotEmpty(ajYxAj)){
            ajxx.setCbbmMc(ajYxAj.getCbbmMc());
            ajxx.setCbjcg(ajYxAj.getCbjcg());
        }
        ajxx.setAnswer(ragAnswer);
        String answer = ragAnswer.getAnswer();
        JSONObject parsed = JSON.parseObject(answer);
        try {
            if(parsed.containsKey("msg")){
                ajxx.setFxsbqk(parsed.getString("msg"));
            }
            if(parsed.containsKey("tags")){
                ajxx.setTags(JSON.parseArray(parsed.getJSONArray("tags").toString(), TyywAjxx.Tag.class));
            }
        } catch (Exception e) {
          log.error("解析风险识别信息失败",e);
        }
        tyywAjxxMapper.insertOrUpdate(ajxx);

    }



}
