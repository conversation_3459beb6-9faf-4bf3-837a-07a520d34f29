package com.smxz.dakongshen.service;

import com.smxz.commons.dto.PageDTO;
import com.smxz.commons.dto.PageRequest;
import com.smxz.commons.exception.BusinessException;
import com.smxz.dakongshen.dto.response.ChatResponse;
import com.smxz.dakongshen.util.AshenAssistantChatIdHolder;
import com.smxz.ragflow.model.BaseResponse;
import com.smxz.ragflow.model.GeneralListRequest;
import com.smxz.ragflow.model.session.RagAnswer;
import com.smxz.ragflow.model.session.RagQuestion;
import com.smxz.ragflow.model.session.SessionCreateRequest;
import com.smxz.ragflow.model.session.SessionData;
import com.smxz.ragflow.service.ChatAssistantService;
import com.smxz.ragflow.service.SessionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class KnowledgeBaseService {

    @Autowired
    private ChatAssistantService chatAssistantService;

    @Autowired
    private SessionService sessionService;

    public Flux<ChatResponse> chat(String sessionId, String message) {
        RagQuestion ragQuestion = RagQuestion.builder()
                .sessionId(sessionId)
                .userId("robot")
                .question(message)
                .build();
        Flux<BaseResponse<RagAnswer>> ragAnswerBaseResponse = sessionService.converseWhitChatAssistantStream(AshenAssistantChatIdHolder.getChatId(), ragQuestion, false);

        return ragAnswerBaseResponse
                .map(e -> {
                    System.out.println(e);
                    return e;
                })
                .map(e -> e.getData().getAnswer())
                .map(e -> {
                    ChatResponse cr = new ChatResponse();
                    cr.setV(e);

                    return cr;
                });
    }

    public PageDTO<SessionData> pageHistorySession(PageRequest request) {
        GeneralListRequest generalListRequest = GeneralListRequest.builder()
                .userId("robot")
                .page(request.getCurrPage().intValue())
                .pageSize(request.getPageSize().intValue())
                .build();

        BaseResponse<List<SessionData>> response = sessionService.listSessionsWithChat(AshenAssistantChatIdHolder.getChatId(), generalListRequest);

        PageDTO<SessionData> result = new PageDTO<>();
        result.setData(response.getData());
        result.setCurrPage(request.getCurrPage());
        result.setPageSize(request.getPageSize());

        return result;
    }

    public SessionData createChat(String sessionName) {
        SessionCreateRequest request = SessionCreateRequest.builder()
                .userId("robot")
                .name(sessionName)
                .build();
        BaseResponse<SessionData> response = sessionService.createSessionWithChat(AshenAssistantChatIdHolder.getChatId(), request);

        return response.getData();
    }

    public void deleteChat(String sessionId) {
        BaseResponse<Void> response = sessionService.deleteSessionWithChat(AshenAssistantChatIdHolder.getChatId(), Collections.singletonList(sessionId));
        if (response.getCode() != 0) {
            throw new BusinessException("删除失败");
        }
    }

    public void updateChat(String sessionId, String sessionName) {
        BaseResponse<Void> response = sessionService.updateSessionWithChat(AshenAssistantChatIdHolder.getChatId(), sessionId, sessionName);
        if (response.getCode() != 0) {
            throw new BusinessException("更新失败");
        }
    }
}
