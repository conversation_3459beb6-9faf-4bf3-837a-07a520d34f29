package com.smxz.dakongshen.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.smxz.dakongshen.dto.response.AllSyCount;
import com.smxz.dakongshen.dto.response.MonthlyCountItem;
import com.smxz.dakongshen.dto.response.NameValueItem;
import com.smxz.dakongshen.entity.PetitionDataRaw;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 信访原始数据服务接口
 */
public interface IPetitionDataRawService extends IService<PetitionDataRaw> {
    
    /**
     * 分页查询信访数据
     * @param page 分页参数
     * @param petitionType 信访类型（可选）
     * @param petitionChannel 信访渠道（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<PetitionDataRaw> pageQuery(Page<PetitionDataRaw> page, String petitionType, 
                                     String petitionChannel, LocalDateTime startTime, 
                                     LocalDateTime endTime);
    
    /**
     * 按时间范围统计各类型信访数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 信访类型及对应数量
     */
    List<NameValueItem> countByPetitionMethod(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 按时间范围统计各渠道信访数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 信访渠道及对应数量
     */
    List<NameValueItem> countByPetitionChannel(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 按时间范围统计各区域信访数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 区域及对应信访数量
     */
    List<NameValueItem> countByPetitionLocation(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 按时间范围统计涉案领域分布
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 涉案领域及对应数量
     */
    List<NameValueItem> countByInvolvementField(LocalDateTime startTime, LocalDateTime endTime);


    /**
     * 计算数量
     * @param currentStartTime 当前时间段开始时间
     * @param currentEndTime 当前时间段结束时间
     * @return 数量
     */
    AllSyCount countDpByTimeRange(LocalDateTime currentStartTime, LocalDateTime currentEndTime);

                                   
    /**
     * 按月份统计信访量趋势
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 月度统计数据
     */
    List<MonthlyCountItem> countByMonth(LocalDateTime startTime, LocalDateTime endTime);
} 