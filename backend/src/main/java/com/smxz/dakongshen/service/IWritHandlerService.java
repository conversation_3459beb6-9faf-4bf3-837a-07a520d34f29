package com.smxz.dakongshen.service;

import com.smxz.dakongshen.entity.BaseProperties;
import com.smxz.dakongshen.entity.WritHandlerEntity;
import java.io.InputStream;
import java.util.Optional;

/**
 * <AUTHOR> 诚
 * @description 通用文件下载上传接口
 * @since 2025-06-03 10:35
 **/
public interface IWritHandlerService {

    /**
     * 通用下载接口  后续文书提供方案变化，可新增实现类实现该接口
     * @param entity
     * @param writHandlerEntity
     * @return
     */
    Optional<InputStream> download(WritHandlerEntity writHandlerEntity);



    Optional<Boolean> upload(WritHandlerEntity writHandlerEntity,InputStream in);

}
