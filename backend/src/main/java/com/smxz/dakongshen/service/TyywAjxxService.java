/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.smxz.commons.dto.PageDTO;
import com.smxz.dakongshen.dto.AjxxSearchDTO;
import com.smxz.dakongshen.entity.TyywAjxx;
import com.smxz.dakongshen.mapper.TyywAjxxMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-06 11:31
 **/

@Service
public class TyywAjxxService  extends ServiceImpl<TyywAjxxMapper, TyywAjxx> {


    public PageDTO<TyywAjxx> pageTyywAjxx(Integer page, Integer pageSize, AjxxSearchDTO searchDTO) {

        LambdaQueryWrapper<TyywAjxx> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(searchDTO.getAjmc())) {
            wrapper.like(TyywAjxx::getAjmc, searchDTO.getAjmc());
        }
        if (StringUtils.isNotBlank(searchDTO.getXyrxm())) {
            wrapper.apply("JSON_SEARCH(xyr_list, 'all', '%"+searchDTO.getXyrxm()+"%') IS NOT NULL");
        }
        if (StringUtils.isNotBlank(searchDTO.getCbbmMc())) {
            wrapper.like(TyywAjxx::getCbbmMc, searchDTO.getCbbmMc());
        }
        if (StringUtils.isNotBlank(searchDTO.getCbjcg())) {
            wrapper.like(TyywAjxx::getCbjcg, searchDTO.getCbjcg());
        }
        if (ObjectUtils.isNotEmpty(searchDTO.getSlrqStart()) && ObjectUtils.isNotEmpty(searchDTO.getSlrqEnd())) {
            wrapper.between(TyywAjxx::getSlrq, searchDTO.getSlrqStart(), searchDTO.getSlrqEnd());
        }
        Page<TyywAjxx> paged = page(new Page<>(page, pageSize), wrapper);
        PageDTO<TyywAjxx> pageDTO = new PageDTO<>();
        pageDTO.setData(paged.getRecords());
        pageDTO.setHasPrevious(paged.hasPrevious());
        pageDTO.setCurrPage(paged.getCurrent());
        pageDTO.setPageSize(paged.getSize());
        pageDTO.setTotal(paged.getTotal());
        pageDTO.setHasNext(paged.hasNext());
        pageDTO.setTotalPage(paged.getPages());
        return pageDTO;
    }

}
