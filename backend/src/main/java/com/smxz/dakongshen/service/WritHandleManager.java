package com.smxz.dakongshen.service;

import com.smxz.dakongshen.annotation.WritSource;
import com.smxz.dakongshen.consts.WritSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@Component
@Slf4j
public class WritHandleManager {



    private Map<WritSourceEnum, IWritHandlerService> serviceMap;

    @Autowired
    private void setServiceMap(List<IWritHandlerService> services) {
        if (CollectionUtils.isEmpty(services)) {
            log.warn("can't find any service implement IWritHandlerService");
            return;
        }
        serviceMap = services.stream()
                .filter(service -> {
                    AnnotationUtils.findAnnotation(service.getClass(), WritSource.class);
                    return true;
                })
                .collect(Collectors.toMap(
                    service -> AnnotationUtils.findAnnotation(service.getClass(), WritSource.class).source(),
                    service -> service));
    }

    public IWritHandlerService getHandlerService(String writSource) {
        Optional<WritSourceEnum> writSourceEnumOptional = WritSourceEnum.getBySource(writSource);
        if (writSourceEnumOptional.isPresent()) {
            return serviceMap.get(writSourceEnumOptional.get());
        } else {
            log.warn("文书来源配置有误,或者暂未实现该来源文书处理方案");
            return serviceMap.get(WritSourceEnum.LOCAL);
        }

    }

    public IWritHandlerService getHandlerService(WritSourceEnum writSourceEnum) {
        return serviceMap.get(writSourceEnum);
    }
}
