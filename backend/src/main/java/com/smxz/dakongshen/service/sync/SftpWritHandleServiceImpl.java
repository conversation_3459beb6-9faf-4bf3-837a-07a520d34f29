/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.service.sync;

import com.jcraft.jsch.ChannelSftp;
import com.smxz.dakongshen.annotation.WritSource;
import com.smxz.dakongshen.config.SftpProperties;
import com.smxz.dakongshen.consts.WritSourceEnum;
import com.smxz.dakongshen.entity.BaseProperties;
import com.smxz.dakongshen.entity.WritHandlerEntity;
import com.smxz.dakongshen.service.IWritHandlerService;
import com.smxz.dakongshen.sftp.SftpClientPoolManager;
import com.smxz.dakongshen.sftp.SftpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Optional;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-03 10:58
 **/

@Component
@WritSource(source = WritSourceEnum.SFTP)
@Slf4j
@RequiredArgsConstructor
public class SftpWritHandleServiceImpl implements IWritHandlerService {

    private final SftpUtil sftpUtil;

    private final SftpClientPoolManager poolManager;

    private final SftpProperties properties;
    @Override
    public Optional<InputStream> download(WritHandlerEntity writHandlerEntity) {
        ChannelSftp sftpClient=null;

        try {

            sftpClient = poolManager.getSftpClient(properties);
            InputStream inputStream = sftpUtil.download(sftpClient,writHandlerEntity.getFileFullPath());
            return Optional.of(inputStream);
        } catch (Exception e) {
            log.error("下载文书出现错误,文书路径【{}】",writHandlerEntity.getFileFullPath(),e);
        }finally {
            poolManager.releaseFtpClient(properties,sftpClient);
        }
        return Optional.empty();
    }

    @Override
    public Optional<Boolean> upload(WritHandlerEntity writHandlerEntity, InputStream in) {
        return Optional.empty();
    }
}
