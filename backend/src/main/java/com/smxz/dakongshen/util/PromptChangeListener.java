package com.smxz.dakongshen.util;

import com.smxz.ragflow.model.BaseResponse;
import com.smxz.ragflow.model.assistant.ChatAssistantCreateRequest;
import com.smxz.ragflow.model.assistant.ChatAssistantData;
import com.smxz.ragflow.service.ChatAssistantService;
import com.smxz.smxzpromptmanagespringbootstarter.entity.PromptConfig;
import com.smxz.smxzpromptmanagespringbootstarter.event.PromptConfigChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class PromptChangeListener {

    @Autowired
    private ChatAssistantService chatAssistantService;

    @Value("${ashen.database-id}")
    private String ashenDataBaseId;

    @Value("${ashen.model-name}")
    private String ashenModelName;


    @EventListener
    public void handlePromptConfigChange(PromptConfigChangeEvent event) {
        PromptConfig config = event.getPromptConfig();
        PromptConfigChangeEvent.ChangeType changeType = event.getChangeType();

        if (changeType == PromptConfigChangeEvent.ChangeType.CREATE) {
            // 处理创建事件
            System.out.println("提示词配置已创建: " + config.getCategoryKey() + "-" + config.getKey());
        } else if (changeType == PromptConfigChangeEvent.ChangeType.UPDATE) {
            // 处理更新事件
            if (Objects.equals(config.getCategoryKey(), "caseRiskIdentification") && Objects.equals(config.getKey(), "systemPrompt")) {
                ChatAssistantCreateRequest request = ChatAssistantCreateRequest.builder()
                        .name("阿申")
                        .description("AI问答助手阿申")
                        .datasetIds(List.of(ashenDataBaseId)).build();
                String prompt = config.getContent();
                request.addLlmSetting("model_name", ashenModelName);
                request.addPromptSetting("empty_response", "");
                request.addPromptSetting("opener", "");
                request.addPromptSetting("prompt", prompt);

                BaseResponse<ChatAssistantData> response = chatAssistantService.updateAssistant(AshenAssistantChatIdHolder.getChatId(), request);
                if (response.getCode() != 0) {
                    log.warn("ai助手提示词更新失败！");
                }
            }
        }
    }


}
