package com.smxz.dakongshen.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.smxz.dakongshen.dto.request.ExcelImportData;
import com.smxz.dakongshen.entity.Petition;
import com.smxz.dakongshen.entity.PetitionDataRaw;
import com.smxz.dakongshen.entity.PetitionPetitioner;
import com.smxz.dakongshen.mapper.PetitionDataRawMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Getter
@Setter
@RequiredArgsConstructor
public class PetitionRecordListener extends AnalysisEventListener<ExcelImportData> {

    private static final int BATCH_SIZE = 500;

    private List<PetitionDataRaw> tempData = new ArrayList<>();

    private final PetitionDataRawMapper petitionDataRawMapper;

    private final Long importId;

    @Override
    public void invoke(ExcelImportData data, AnalysisContext context) {
        PetitionDataRaw petitionDataRaw = new PetitionDataRaw();
        BeanUtils.copyProperties(data, petitionDataRaw);
        petitionDataRaw.setImportId(importId);
        tempData.add(petitionDataRaw);
        if (tempData.size() == BATCH_SIZE) {
            saveData();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }

    private void saveData() {
        petitionDataRawMapper.insert(tempData);
        tempData.clear();
    }


}
