/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.util;

import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-03 14:14
 **/
public class FileExtractUtil {

    /**
     * 根据文件后缀名提取文本内容， 目前内容只支持 pdf, doc, docx, ppt, pptx, xls, xlsx 的提取
     * 如果是其它文件的类型，返回 String = null
     * @param inputStream 文件输入流
     * @param fileName 文件名（用于获取后缀）
     * @return 提取的文本内容
     */
    public static String extractString(InputStream inputStream, String fileName) throws IOException {
        String res = null;
        String suffix = getFileSuffix(fileName).toLowerCase();
        switch (suffix) {
            case "doc":
                res = doc2String(inputStream);
                break;
            case "docx":
                res = docx2String(inputStream);
                break;
        }
        return res == null ? "" : res;
    }

    /**
     * 得到文件后缀
     * @param fileName 文件名
     * @return
     */
    public static String getFileSuffix(String fileName) {
        return FilenameUtils.getExtension(fileName);
    }


    public static String doc2String(InputStream inputStream) throws IOException {
        try(WordExtractor extractor = new WordExtractor(inputStream)){
            return extractor.getText();
        } catch (Exception e) {
            throw new IOException("读取doc内容出现异常", e);
        }
    }

    public static String docx2String(InputStream inputStream) throws IOException {
        try(XWPFDocument docx = new XWPFDocument(inputStream);
            XWPFWordExtractor extractor = new XWPFWordExtractor(docx)){
            return extractor.getText();
        } catch (Exception e) {
            throw new IOException("读取docx内容出现异常", e);
        }
    }
}
