package com.smxz.dakongshen.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 信访总体概况响应
 */
@Data
@Schema(description = "信访总体概况响应")
public class VisitOverviewResponse {

    @Schema(description = "全市信访量")
    private Integer qsxfl;

    @Schema(description = "全市信访量同比", example = "5.2")
    private BigDecimal qsxfltb;

    @Schema(description = "广州市院信访量")
    private Integer gzsyxfl;

    @Schema(description = "广州市院信访量同比", example = "-3.1")
    private BigDecimal gzsyxfltb;
} 