package com.smxz.dakongshen.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通用名称-值数据项
 */
@Data
@Schema(description = "数量同比项")
public class AllSyCount {

    @Schema(description = "当期全市数量")
    private Integer currentYearAllCount;
    
    @Schema(description = "上期全市数量")
    private Integer lastYearAllCount;

    @Schema(description = "当期广州市院数量")
    private Integer currentYearSyCount;

    @Schema(description = "上期广州市院数量")
    private Integer lastYearSyCount;
} 