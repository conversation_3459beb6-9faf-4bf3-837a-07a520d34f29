package com.smxz.dakongshen.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PersonInfoResponse {

    @Schema(title = "姓名")
    private String name;

    @Schema(title = "身份证号")
    private String idNumber;

    @Schema(title = "基本案情")
    private String brief;

    @Schema(title = "风险等级")
    private String riskLevel;

    @Schema(title = "性别")
    private String sex;

    @Schema(title = "户籍所在地")
    private String registeredResidence;

    @Schema(title = "联系住址")
    private String contactAddress;

}
