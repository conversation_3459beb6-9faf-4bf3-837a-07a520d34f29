package com.smxz.dakongshen.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日期范围请求参数
 */
@Data
@Schema(description = "风险大屏请求参数")
public class RiskRequest {

    @Schema(description = "开始日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private LocalDateTime startDate;

    @Schema(description = "结束日期", example = "2023-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull
    private LocalDateTime endDate;
} 