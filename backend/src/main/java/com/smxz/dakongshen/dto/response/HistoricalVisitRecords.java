package com.smxz.dakongshen.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class HistoricalVisitRecords {

    @Schema(title = "来访总结")
    private String summary;

    @Schema(title = "来访历史记录")
    private List<HistoricalVisitRecord> visitRecords;

    @Getter
    @Setter
    public static class HistoricalVisitRecord {

        @Schema(title = "案件名称")
        private String caseName;

        @Schema(title = "信访渠道")
        private String petitionChannel;

        @Schema(title = "案件类型")
        private String caseType;

        @Schema(title = "案情摘要")
        private String brief;

        @Schema(title = "回复内容")
        private String replyContent;

        @Schema(title = "承办单位")
        private String organizer;

        @JsonFormat(pattern = "yyyy-MM-dd")
        @Schema(title = "来访时间")
        private LocalDateTime visitTime;

    }
}
