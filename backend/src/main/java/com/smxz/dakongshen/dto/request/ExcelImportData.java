package com.smxz.dakongshen.dto.request;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ExcelImportData {

    @ExcelProperty("信访次数")
    private String petitionCount;

    @ExcelProperty("信访时间")
    private String petitionTime;

    @ExcelProperty("承办单位")
    private String organizer;

    @ExcelProperty("登记人")
    private String registrant;

    @ExcelProperty("案件名称")
    private String caseName;

    @ExcelProperty("信访类型")
    private String petitionType;

    @ExcelProperty("重复信访")
    private String repeatedPetition;

    @ExcelProperty("重复信访治理")
    private String repeatedPetitionGovernance;

    @ExcelProperty("信访人姓名")
    private String petitionerName;

    @ExcelProperty("案发地")
    private String petitionLocation;

    @ExcelProperty("信访渠道")
    private String petitionChannel;

    @ExcelProperty("信访方式")
    private String petitionMethod;

    @ExcelProperty("案件来源")
    private String caseSource;

    @ExcelProperty("涉案领域")
    private String involvementField;

    @ExcelProperty("涉检访类型")
    private String sjflx;

    @ExcelProperty("接收单位")
    private String receivingOrganization ;

    @ExcelProperty("分流/办理情况")
    private String processingStatus;

    @ExcelProperty("承办人")
    private String contractor;

    @ExcelProperty("案情摘要")
    private String brief;

    @ExcelProperty("条码")
    private String barCode;

    @ExcelProperty("被信访人姓名")
    private String respondentName;

    @ExcelProperty("部门受案号")
    private String departmentAcceptanceNumber;
}
