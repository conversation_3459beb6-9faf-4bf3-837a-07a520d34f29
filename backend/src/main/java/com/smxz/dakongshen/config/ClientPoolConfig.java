/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.config;

import com.jcraft.jsch.ChannelSftp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.pool2.impl.GenericKeyedObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-03 10:11
 **/
@Slf4j
@ToString
@Component
public class ClientPoolConfig {


    private static final int DEFAULT_MAX_TOTAL_PER_KEY = -1;

    private static final int DEFAULT_MAX_TOTAL = -1;

    private static final int DEFAULT_MIN_IDLE_PER_KEY = 20;

    private static final int DEFAULT_MAX_IDLE_PER_KEY = 100;

    private static final long DEFAULT_MAX_WAIT_MILLIS = -1L;

    private static final long DEFAULT_TIME_BETWEEN_EVICTION_RUNS_MILLIS = 1000 * 10L;

    /**
     * 每个 key 对应的 FTP 连接池最小空闲连接数，默认 {@code 20}.
     */
    @Getter
    @Setter
    private String minIdlePerKey;

    /**
     * 每个 key 对应的 SFTP 连接池最大空闲连接数，默认 {@code 50}.
     */
    @Getter
    @Setter
    private String maxIdlePerKey;

    /**
     * 每个 key 对应的 SFTP 连接池最大连接数，默认不限制.
     */
    @Getter
    @Setter
    private String maxTotalPerKey;

    /**
     * FTP 连接池最大连接数，默认不限制.
     */
    @Getter
    @Setter
    private String maxTotal;

    /**
     * 获取连接最大等待时间，单位毫秒，默认不限制.
     */
    @Getter
    @Setter
    private String maxWaitMillis;

    /**
     * 每隔多长时间运行一次空闲连接回收器，单位毫秒，默认：{@code 10000} 毫秒.
     */
    @Getter
    @Setter
    private String timeBetweenEvictionRunsMillis;

    /**
     * 构建连接池的相关配置信息.
     *
     * @return {@link GenericKeyedObjectPoolConfig} 配置好实例
     */
    public GenericKeyedObjectPoolConfig<ChannelSftp> buildSftpPoolConfig() {
        GenericKeyedObjectPoolConfig<ChannelSftp> config = new GenericKeyedObjectPoolConfig<>();
        config.setTestOnBorrow(true);
        config.setTestWhileIdle(true);
        config.setMaxTotal(NumberUtils.toInt(this.maxTotal, DEFAULT_MAX_TOTAL));
        config.setMaxTotalPerKey(NumberUtils.toInt(this.maxTotalPerKey, DEFAULT_MAX_TOTAL_PER_KEY));
        config.setMaxIdlePerKey(NumberUtils.toInt(this.maxIdlePerKey, DEFAULT_MAX_IDLE_PER_KEY));
        config.setMinIdlePerKey(NumberUtils.toInt(this.minIdlePerKey, DEFAULT_MIN_IDLE_PER_KEY));
        config.setMaxWaitMillis(NumberUtils.toLong(this.maxWaitMillis, DEFAULT_MAX_WAIT_MILLIS));
        config.setTimeBetweenEvictionRunsMillis(NumberUtils.toLong(this.timeBetweenEvictionRunsMillis,
                DEFAULT_TIME_BETWEEN_EVICTION_RUNS_MILLIS));
        log.debug("【SFTP Client Pool 提示】SFTP 的连接池信息为：【{}】.", this);
        return config;
    }

}
