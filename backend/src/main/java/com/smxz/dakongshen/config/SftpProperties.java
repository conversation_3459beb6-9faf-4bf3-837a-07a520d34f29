/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.config;
import com.smxz.dakongshen.entity.BaseProperties;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-03 10:08
 **/
@Setter
@ConfigurationProperties(prefix = "sftp")
@Component
public class SftpProperties implements BaseProperties {

    private String host;
    private int port;
    private String username;
    private String password;
    private String workDirectory;

    @Override
    public String getHost() {
        return host;
    }

    @Override
    public int getPort() {
        return port;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getWorkDirectory() {
        return workDirectory;
    }
}
