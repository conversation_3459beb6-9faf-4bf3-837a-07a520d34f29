package com.smxz.dakongshen;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@SpringBootApplication
@EnableScheduling
@MapperScan("com.smxz.prompt.mapper")
@MapperScan("com.smxz.dakongshen.mapper")
public class DakongshenApplication {

	public static void main(String[] args) {
		SpringApplication.run(DakongshenApplication.class, args);
		log.debug("http://localhost:8081/doc.html");
	}

}
