/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.consts;

import lombok.Getter;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-05-29 11:57
 **/
@Getter
public enum TaskStatus {

    INIT(0, "初始化"),
    PROCESSING(1, "处理中"),
    FINISHED(2, "已完成");

    private final int code;
    private final String description;

    TaskStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
