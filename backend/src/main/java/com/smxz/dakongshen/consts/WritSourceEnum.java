package com.smxz.dakongshen.consts;

import java.util.Optional;


public enum WritSourceEnum {

    /**
     *
     */
    FTP("ftp"),
    /**
     * 共享目录
     */
    SMB("smb"),
    /**
     * 本地
     */
    LOCAL("local"),
    /**
     * minIO
     */
    S3("s3"),
    /**
     *
     */
    OSS("oss"),
    /**
     * 流程办案接口
     */
    LCBA("lcba"),

    /**
     * sftp
     */
    SFTP("sftp"),

    /**
     * sftp
     */
    TCS("tcs");

    private String source;

    WritSourceEnum(String source) {
        this.source = source;
    }

    public static Optional<WritSourceEnum> getBySource(String version) {
        for (WritSourceEnum writSourceEnum : WritSourceEnum.values()) {
            if (writSourceEnum.source.equalsIgnoreCase(version)) {
                return Optional.of(writSourceEnum);
            }
        }
        return Optional.empty();
    }
}
