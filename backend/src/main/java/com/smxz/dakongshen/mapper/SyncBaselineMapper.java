/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.dakongshen.entity.SyncBaseline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-10 14:23
 **/
@Mapper
public interface SyncBaselineMapper  extends BaseMapper<SyncBaseline> {



    @Select("""
            SELECT  * FROM  sync_baseline WHERE  id =( SELECT  max(id) from sync_baseline )
            """)
    SyncBaseline selectLatestSyncBaseline();

}
