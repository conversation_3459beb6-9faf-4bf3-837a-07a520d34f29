package com.smxz.dakongshen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.dakongshen.entity.Petition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PetitionMapper extends BaseMapper<Petition> {

    // 通过信访人名称搜索信访案件
    List<Petition> listPetitionByPetitionerName(@Param("petitionerName") String petitionerName);

}