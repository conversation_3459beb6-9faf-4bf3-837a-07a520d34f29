/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.mapper.tyyw;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.smxz.dakongshen.entity.AjYxAj;
import com.smxz.dakongshen.entity.TyywAjxx;
import com.smxz.dakongshen.entity.TyywXyr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-05 09:36
 **/

@DS("tyyw")
@Mapper
public interface SyncTyywAjxxBaseMapper {

    /**
     * 查询审查逮捕表中数据
     * @param bmsah
     * @return
     */
    TyywAjxx selectScdbByBmsah(@Param("bmsah") String bmsah);

    /**
     * 查询审查起诉表中数据
     * @param bmsah
     * @return
     */
    TyywAjxx selectScqsByBmsah(@Param("bmsah") String bmsah);


    List<TyywXyr> selectScdbXyrByBmsah(@Param("bmsah") String bmsah);


    List<TyywXyr> selectScqsXyrByBmsah(@Param("bmsah") String bmsah);

    String selectAqzyByBmsah(@Param("bmsah") String bmsah);

    AjYxAj selectAjYxAj(@Param("bmsah") String bmsah);
}
