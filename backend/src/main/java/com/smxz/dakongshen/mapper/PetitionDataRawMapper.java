package com.smxz.dakongshen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.smxz.dakongshen.dto.response.AllSyCount;
import com.smxz.dakongshen.dto.response.MonthlyCountItem;
import com.smxz.dakongshen.dto.response.NameValueItem;
import com.smxz.dakongshen.entity.PetitionDataRaw;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface PetitionDataRawMapper extends BaseMapper<PetitionDataRaw> {
    
    /**
     * 按信访类型统计数量
     */
    List<NameValueItem> countByPetitionMethod(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按信访渠道统计数量
     */
    List<NameValueItem> countByPetitionChannel(@Param("startTime") LocalDateTime startTime, 
                                              @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按信访地区统计数量
     */
    List<NameValueItem> countByPetitionLocation(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按涉案领域统计数量
     */
    List<NameValueItem> countByInvolvementField(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按时间段统计信访总量
     */
    Integer countByTimeRange(@Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);

    AllSyCount countDpByTimeRange(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);
    
    /**
     * 按月份统计信访量趋势
     */
    List<MonthlyCountItem> countByMonth(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);
}
