/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.smxz.dakongshen.consts.TaskStatus;
import com.smxz.dakongshen.entity.SyncBaseline;
import com.smxz.dakongshen.entity.SyncTask;
import com.smxz.dakongshen.entity.tyyw.Ajjzwj;
import com.smxz.dakongshen.service.SyncBaselineService;
import com.smxz.dakongshen.service.SyncTaskService;
import com.smxz.dakongshen.service.tyyw.JzmlwjService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 诚
 * @description 案件信息同步定时任务
 * @since 2025-05-28 14:15
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class AjxxSyncScheduler {


    private final SyncTaskService syncTaskService;

    private final SyncBaselineService baselineService;

    private final JzmlwjService jzmlwjService;

    private  ExecutorService threadPool;

    @PostConstruct
    public void init() {
        // 创建有界线程池
        threadPool = new ThreadPoolExecutor(
                4, // 核心线程数
                8, // 最大线程数(可适当放大)
                60L, TimeUnit.SECONDS, // 空闲线程存活时间
                new LinkedBlockingQueue<>(500), // 扩大任务队列
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
        log.info("初始化线程池");
    }

    @Scheduled(cron = "${scheduler.sync.ajxx.expression}")
    @SchedulerLock(name = "syncAjxx", lockAtMostFor = "60m")
    public void syncAjxx() {
        log.info("案件信息风险分析定时任务开始");
        SyncBaseline baseline = baselineService.getBaseMapper().selectLatestSyncBaseline();
        long start = System.currentTimeMillis();
        LocalDateTime lastExecuteTime = baseline.getLastExecuteTime();

        List<Ajjzwj> ajjzwjs = jzmlwjService.listAjjzwj(lastExecuteTime, LocalDateTime.now());
        SyncBaseline newBaseline = SyncBaseline.builder().lastExecuteTime(LocalDateTime.now()).build();
        try {
            syncTaskService.batchAnalyzeAjjzwj(ajjzwjs,null);
        } catch (IOException e) {
            log.error("案件信息风险分析定时任务异常", e);
            throw new RuntimeException(e);
        }
        newBaseline.setExecuteFinishTime(LocalDateTime.now());
        baselineService.saveOrUpdate(newBaseline);
        log.info("案件信息风险分析定时任务完成，一共{}条数据，共计耗时{}ms",ajjzwjs.size(),System.currentTimeMillis() - start);
    }
}
