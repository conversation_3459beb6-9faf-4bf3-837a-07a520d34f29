/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.handler;

import com.smxz.dakongshen.config.MinioConfig;
import com.smxz.dakongshen.util.UUIDHelper;
import com.smxz.onlyoffice.model.ConversionRequest;
import com.smxz.onlyoffice.model.ConversionResult;
import com.smxz.onlyoffice.service.DocumentService;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> 诚
 * @description
 * @since 2025-06-10 18:44
 **/
@Slf4j
@Component
@Order(1)
@RequiredArgsConstructor
public class DocxToPdfHandler {
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final DocumentService documentService;
    private final RestTemplate restTemplate;



    @Value("${minio.bucketName:uploads}")
    private String minioBucketName;

    @PostConstruct
    public void init() {
        ensureBucketExists();
    }
    private void ensureBucketExists() {
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioBucketName).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(minioBucketName).build());
            }
        } catch (Exception e) {
            log.error("MinIO 存储桶初始化失败", e);
            throw new RuntimeException("MinIO 存储桶初始化失败", e);
        }
    }

    public ObjectWriteResponse doUploadFile(InputStream inputStream, String fileName) {
        try  {
            String s3filename = UUIDHelper.generateUUID()+"." +FilenameUtils.getExtension(fileName);
            MediaType mediaType = MediaTypeFactory.getMediaType(fileName).orElse(MediaType.APPLICATION_OCTET_STREAM);
            return minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioConfig.getBucketName())
                            .object(s3filename)
                            .stream(inputStream, inputStream.available(), -1)
                            .contentType(mediaType.toString())
                            .build());
        } catch (Exception e) {
            log.error("上传文件失败: " + fileName, e);
            throw new RuntimeException("上传文件失败: " + fileName, e);
        }
    }

    /**
     * 将文件转换为PDF并上传到MinIO
     */
    public ObjectWriteResponse doc2PdfWithUpload(String docObjectName,String fileName) {
        log.info("开始转换文档为PDF: {}", fileName);
        try {
            // 获取原文件的预签名URL
            String objectUrl = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioConfig.getBucketName())
                    .object(docObjectName)
                    .build());

            // 获取文件扩展名
            String fileExtension = FilenameUtils.getExtension(fileName);

            // 根据文件扩展名设置转换参数
            String fileType = getFileType(fileExtension);

            // 调用OnlyOffice转换文档
            ConversionResult document = convertDocument(objectUrl, docObjectName, fileType);

            // 下载并上传PDF文件
            ObjectWriteResponse response = uploadConvertedPdf(document.getFileUrl(), docObjectName);
            log.info("PDF转换并上传成功: {}", response.object());
            return response;
        } catch (ErrorResponseException | NoSuchAlgorithmException | ServerException | XmlParserException |
                 IOException | InvalidResponseException | InvalidKeyException | InternalException |
                 InsufficientDataException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取文件类型
     * @param fileExtension 文件扩展名
     * @return 文件类型
     */
    private String getFileType(String fileExtension) {
        switch (fileExtension) {
            case "doc":
                return "doc";
            case "docx":
                return "docx";
            case "wps":
                return "wps";
            default:
                throw new IllegalArgumentException("不支持的文件格式: " + fileExtension);
        }
    }

    /**
     * 转换文档
     * @param objectUrl 原文件URL
     * @param fileName 文件名
     * @param fileType 文件类型
     * @return 转换结果
     */
    private ConversionResult convertDocument(String objectUrl, String fileName, String fileType) {
        ConversionRequest conversionRequest = ConversionRequest.builder()
                .async(false)
                .pdf(ConversionRequest.PdfOptions.builder().build())
                .filetype(fileType)
                .key(UUID.randomUUID().toString())
                .outputtype("pdf")
                .url(objectUrl)
                .title(fileName)
                .build();
        return documentService.convertDocument(conversionRequest);
    }

    /**
     * 下载并上传转换后的PDF文件
     * @param pdfUrl PDF文件URL
     * @param originalFileName 原始文件名
     */
    private ObjectWriteResponse uploadConvertedPdf(String pdfUrl, String originalFileName) {
        // 下载转换后的PDF文件
        byte[] pdfBytes = restTemplate.getForObject(pdfUrl, byte[].class);
        if (pdfBytes.length == 0) {
            throw new RuntimeException("Failed to download converted PDF file");
        }

        // 生成PDF文件名
        String pdfFileName = FilenameUtils.removeExtension(originalFileName) + ".pdf";

        // 上传PDF文件到MinIO
        try {
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(pdfFileName)
                    .stream(new ByteArrayInputStream(pdfBytes), pdfBytes.length, -1)
                    .contentType("application/pdf")
                    .build());
            log.info("PDF文件已上传到MinIO: {}", pdfFileName);
            return response;
        } catch (ErrorResponseException | XmlParserException | ServerException | NoSuchAlgorithmException |
                 IOException | InvalidResponseException | InvalidKeyException | InternalException |
                 InsufficientDataException e) {
            throw new RuntimeException(e);
        }
    }


}
