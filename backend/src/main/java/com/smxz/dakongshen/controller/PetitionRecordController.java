package com.smxz.dakongshen.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.smxz.commons.vo.Result;
import com.smxz.dakongshen.mapper.PetitionMapper;
import com.smxz.dakongshen.mapper.PetitionPetitionerMapper;
import com.smxz.dakongshen.mapper.PetitionerPortraitMapper;
import com.smxz.dakongshen.mapper.PetitionerTagMapper;
import com.smxz.dakongshen.service.PetitionRecordService;
import com.smxz.dakongshen.util.AshenAssistantChatIdHolder;
import com.smxz.ragflow.model.assistant.ChatAssistantCreateRequest;
import com.smxz.ragflow.service.ChatAssistantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "信访记录")
@RequestMapping("/petitionRecord")
@RestController
public class PetitionRecordController {

    @Autowired
    private PetitionRecordService petitionRecordService;

    @Operation(summary = "excel导入")
    @PostMapping("/importExcel")
    public Result importExcel(@RequestBody MultipartFile file) {
        petitionRecordService.importExcel(file);

        return Result.ok();
    }

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private PetitionerTagMapper petitionerTagMapper;

    @Autowired
    private PetitionPetitionerMapper petitionPetitionerMapper;

    @Autowired
    private PetitionerPortraitMapper petitionerPortraitMapper;

    @Autowired
    private PetitionMapper petitionMapper;

    @Operation(summary = "工具接口：删除用户画像数据和案件数据")
    @GetMapping("/deleteData")
    public Result deleteData() {
        transactionTemplate.execute(status -> {
            petitionerTagMapper.delete(Wrappers.emptyWrapper());
            petitionPetitionerMapper.delete(Wrappers.emptyWrapper());
            petitionerPortraitMapper.delete(Wrappers.emptyWrapper());
            petitionMapper.delete(Wrappers.emptyWrapper());

            return 1;
        });

        return Result.ok();
    }

    @Autowired
    private ChatAssistantService chatAssistantService;

    @Operation(summary = "工具接口：修改阿申提示词")
    @GetMapping("/updateAssistantPrompt")
    public Result updateAssistantPrompt(String prompt) {
        ChatAssistantCreateRequest request = ChatAssistantCreateRequest.builder()
                .name("阿申")
                .build();

        request.addPromptSetting("prompt", prompt);

        chatAssistantService.updateAssistant(AshenAssistantChatIdHolder.getChatId(), request);
        return Result.ok();
    }

}
