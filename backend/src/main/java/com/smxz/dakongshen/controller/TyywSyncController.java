/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.smxz.commons.dto.PageDTO;
import com.smxz.commons.vo.DataResult;
import com.smxz.commons.vo.PageResult;
import com.smxz.dakongshen.consts.TaskStatus;
import com.smxz.dakongshen.entity.SyncTask;
import com.smxz.dakongshen.service.SyncTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR> 诚
 * @description 统一业务数据同步接口
 * @since 2025-05-29 10:27
 **/
@Tag(name = "统一业务数据同步触发类")
@RequestMapping("api/caseRiskIdentification")
@RestController
@RequiredArgsConstructor
public class TyywSyncController {


    private final SyncTaskService syncTaskService;
    /**
     * @description 初始化同步任务
     * <AUTHOR> 诚
     * @since 2025-05-29 10:27
     **/
    @Operation(summary = "初始化同步任务")
    @PostMapping("initSyncTask")
    public List<SyncTask> initSyncTask(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate){
        // 校验日期范围
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("起始日期不能晚于结束日期");
        }
        List<SyncTask> syncTasks = syncTaskService.splitIntoMonthlyTasks(startDate, endDate);
        syncTaskService.saveBatch(syncTasks);
        return syncTasks;
    }


    /**
     * @description 初始化同步任务
     * <AUTHOR> 诚
     * @since 2025-05-29 10:27
     **/
    @Operation(summary = "获取所有的同步任务")
    @PostMapping("listSyncTask")
    public PageResult<SyncTask> listSyncTask(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                             @RequestParam(value = "pageSize", defaultValue = "12") Integer pageSize){
        Page<SyncTask> paged = syncTaskService.page(new Page<>(page, pageSize));
        PageDTO<SyncTask> pageDTO = new PageDTO<>();
        pageDTO.setData(paged.getRecords());
        pageDTO.setHasPrevious(paged.hasPrevious());
        pageDTO.setCurrPage(paged.getCurrent());
        pageDTO.setPageSize(paged.getSize());
        pageDTO.setTotal(paged.getTotal());
        pageDTO.setHasNext(paged.hasNext());
        pageDTO.setTotalPage(paged.getPages());
        return PageResult.ok(pageDTO);
    }


    /**
     * @description 初始化同步任务
     * <AUTHOR> 诚
     * @since 2025-05-29 10:27
     **/
    @Operation(summary = "执行单个任务")
    @PostMapping("startSingleSyncTask")
    public DataResult startSingleSyncTask(@RequestParam (value = "id") Long id){

        SyncTask task = syncTaskService.getById(id);
        if (task == null) {
            return DataResult.fail("同步任务不存在");
        }
        if (TaskStatus.PROCESSING.getCode()==task.getStatus()) {
            return DataResult.fail("该任务正在执行中，请勿重复操作");
        }
        syncTaskService.startSyncTask(task);
        return DataResult.ok("任务启动成功");
    }


}
