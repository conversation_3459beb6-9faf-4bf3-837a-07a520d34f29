package com.smxz.dakongshen.controller;

import com.smxz.commons.dto.PageRequest;
import com.smxz.commons.vo.DataResult;
import com.smxz.commons.vo.PageResult;
import com.smxz.commons.vo.Result;
import com.smxz.dakongshen.dto.request.CreateSessionRequest;
import com.smxz.dakongshen.dto.request.DeleteSessionRequest;
import com.smxz.dakongshen.dto.request.KnowledgeBaseChatRequest;
import com.smxz.dakongshen.dto.request.UpdateSessionRequest;
import com.smxz.dakongshen.dto.response.ChatResponse;
import com.smxz.dakongshen.service.KnowledgeBaseService;
import com.smxz.ragflow.model.session.SessionData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@Tag(name = "知识库")
@RequestMapping("api/knowledgeBase")
@RestController
public class KnowledgeBaseController {

    @Autowired
    private ChatModel chatModel;

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "聊天")
    @PostMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<ChatResponse>> chat(@RequestBody KnowledgeBaseChatRequest request) {
        Flux<ServerSentEvent<ChatResponse>> flux = knowledgeBaseService.chat(request.getSessionId(), request.getMessage())
                .map(e -> ServerSentEvent.<ChatResponse>builder().event("message").data(e).build());

        ServerSentEvent<ChatResponse> endEvent = ServerSentEvent.<ChatResponse>builder()
                .event("close") // 自定义事件类型
                .build();
        return flux.concatWithValues(endEvent);
    }

    @Operation(summary = "历史聊天记录")
    @PostMapping("/listHistoryChat")
    public PageResult<SessionData> listHistoryChats(@RequestBody PageRequest request) {
        return PageResult.ok(knowledgeBaseService.pageHistorySession(request));
    }

    @Operation(summary = "新建聊天")
    @PostMapping("/createChat")
    public DataResult<SessionData> createChat(@RequestBody CreateSessionRequest request) {
        return DataResult.ok(knowledgeBaseService.createChat(request.getSessionName()));
    }

    @Operation(summary = "删除聊天")
    @PostMapping("/deleteChat")
    public Result deleteChat(@RequestBody DeleteSessionRequest request) {
        knowledgeBaseService.deleteChat(request.getSessionId());
        return Result.ok();
    }

    @Operation(summary = "更新聊天")
    @PostMapping("/updateChat")
    public Result deleteChat(@RequestBody UpdateSessionRequest request) {
        knowledgeBaseService.updateChat(request.getSessionId(), request.getSessionName());
        return Result.ok();
    }

}
