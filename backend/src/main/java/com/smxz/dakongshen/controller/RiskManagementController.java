package com.smxz.dakongshen.controller;

import com.smxz.dakongshen.dto.request.RiskRequest;
import com.smxz.dakongshen.dto.response.CaseAnalysisItem;
import com.smxz.dakongshen.dto.response.NameValueItem;
import com.smxz.dakongshen.dto.response.VisitOverviewResponse;
import com.smxz.dakongshen.service.RiskManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 信访风险源头治理Controller
 */
@Tag(name = "信访风险源头治理")
@RequestMapping("/api/riskManagement")
@RestController
public class RiskManagementController {

    @Autowired
    private RiskManagementService riskManagementService;

    /**
     * 信访渠道分析
     */
    @Operation(summary = "信访渠道分析")
    @PostMapping("/visitChannelAnalysis")
    public ResponseEntity<List<NameValueItem>> visitChannelAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getVisitChannelAnalysis(request));
    }

    /**
     * 信访风险类型分析2
     */
    @Operation(summary = "信访风险类型分析")
    @PostMapping("/visitTopTypeAnalysis")
    public ResponseEntity<List<NameValueItem>> visitTopTypeAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getVisitTopTypeAnalysis(request));
    }

    /**
     * 信访事项涉及罪名
     */
    @Operation(summary = "信访事项涉及罪名")
    @PostMapping("/crimeTypeAnalysis")
    public ResponseEntity<List<NameValueItem>> crimeTypeAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getCrimeTypeAnalysis(request));
    }

    /**
     * 地图数据
     */
    @Operation(summary = "地图数据")
    @PostMapping("/mapData")
    public ResponseEntity<List<NameValueItem>> mapData(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getMapData(request));
    }

    /**
     * 全市信访量和广州市院信访量
     */
    @Operation(summary = "全市信访量和广州市院信访量")
    @PostMapping("/visitOverview")
    public ResponseEntity<VisitOverviewResponse> visitOverview(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getVisitOverview(request));
    }

    /**
     * 信访领域分析
     */
    @Operation(summary = "信访领域分析")
    @PostMapping("/visitFieldAnalysis")
    public ResponseEntity<List<NameValueItem>> visitFieldAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getVisitFieldAnalysis(request));
    }

    /**
     * 信访人身分析
     */
    @Operation(summary = "信访人身分析")
    @PostMapping("/personAnalysis")
    public ResponseEntity<List<NameValueItem>> personAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getPersonAnalysis(request));
    }

    /**
     * 涉检情况分析(成案)
     */
    @Operation(summary = "涉检情况分析(成案)")
    @PostMapping("/caseAnalysis")
    public ResponseEntity<List<CaseAnalysisItem>> caseAnalysis(@RequestBody @Validated RiskRequest request) {
        return ResponseEntity.ok(riskManagementService.getCaseAnalysis(request));
    }
} 