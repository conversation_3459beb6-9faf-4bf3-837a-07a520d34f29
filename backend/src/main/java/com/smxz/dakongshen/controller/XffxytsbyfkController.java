/**
 * Copyright© 2025 水木星洲. All rights reserved.
 **/
package com.smxz.dakongshen.controller;

import com.smxz.commons.dto.PageDTO;
import com.smxz.commons.vo.DataResult;
import com.smxz.commons.vo.PageResult;
import com.smxz.dakongshen.config.MinioConfig;
import com.smxz.dakongshen.dto.AjxxSearchDTO;
import com.smxz.dakongshen.entity.TyywAjxx;
import com.smxz.dakongshen.service.TyywAjxxService;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.MinioClient;
import io.minio.errors.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> 诚
 * @description  信访风险来源识别与防控
 * @since 2025-06-06 11:27
 **/
@Tag(name = "信访风险源头识别与防控")
@RequestMapping("api/xffxytsbyfk")
@RestController
@RequiredArgsConstructor
public class XffxytsbyfkController {

    private  final TyywAjxxService  tyywAjxxService;


    private final MinioClient minioClient;

    private final MinioConfig minioConfig;


    @Operation(summary = "统一业务涉信访风险案件信息")
    @PostMapping("/tyywAjxxList")
    public PageResult<TyywAjxx> tyywAjxxList(@RequestParam(value = "page", defaultValue = "1") Integer page,
                                             @RequestParam(value = "pageSize", defaultValue = "12") Integer pageSize,
                                             @RequestBody AjxxSearchDTO searchDTO) {

        PageDTO<TyywAjxx> pageDTO = tyywAjxxService.pageTyywAjxx(page, pageSize, searchDTO);
        return PageResult.ok(pageDTO);
    }

    @Operation(summary = "获取单个业务涉信访风险案件信息")
    @GetMapping(value = "/getTyywAjxx/{bmsah}")
    public DataResult<TyywAjxx> getTyywAjxx(@PathVariable("bmsah") String bmsah) {
        TyywAjxx ajxx = tyywAjxxService.getById(bmsah);
        return DataResult.ok(ajxx);
    }


    @GetMapping("/download/stream/{bmsah}")
    public ResponseEntity<StreamingResponseBody> getFileStream(@PathVariable("bmsah") String bmsah) {
        TyywAjxx ajxx = tyywAjxxService.getById(bmsah);
        StreamingResponseBody responseBody = outputStream -> {
            // 获取文件流并写入outputStream
            try (GetObjectResponse object = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(ajxx.getPdfObject())
                    .build());) {
                object.transferTo(outputStream);
            } catch (ServerException | InsufficientDataException | ErrorResponseException | NoSuchAlgorithmException |
                     InvalidKeyException | InvalidResponseException | XmlParserException | InternalException e) {
                throw new RuntimeException(e);
            }
        };
        // URL编码文件名
        String encodedFileName = URLEncoder.encode(ajxx.getWjmc(), StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20"); // 将+替换为%20，保持空格的可读性
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header("Content-Disposition", "inline; filename=\"" + encodedFileName + "\"")
                .body(responseBody);
    }
}
