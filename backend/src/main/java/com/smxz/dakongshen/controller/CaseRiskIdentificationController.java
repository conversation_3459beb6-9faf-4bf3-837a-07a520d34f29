package com.smxz.dakongshen.controller;

import com.smxz.commons.vo.DataResult;
import com.smxz.dakongshen.dto.RecommendedCaseDetail;
import com.smxz.dakongshen.dto.request.CaseRiskIdentificationRequest;
import com.smxz.dakongshen.dto.request.HistoryPetitionRequest;
import com.smxz.dakongshen.dto.request.PersonTagsAndSummaryRequest;
import com.smxz.dakongshen.dto.request.RecommendedCasesRequest;
import com.smxz.dakongshen.dto.response.HistoricalVisitRecords;
import com.smxz.dakongshen.dto.response.PersonInfoResponse;
import com.smxz.dakongshen.dto.response.PersonRiskResponse;
import com.smxz.dakongshen.dto.response.RecommendedCase;
import com.smxz.dakongshen.entity.CaseRiskIdentificationHistory;
import com.smxz.dakongshen.service.CaseRiskIdentificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "案中风险识别")
@RequestMapping("api/caseRiskIdentification")
@RestController
public class CaseRiskIdentificationController {

    @Autowired
    private CaseRiskIdentificationService caseRiskIdentificationService;

    @Operation(summary = "风险识别")
    @PostMapping("/identification")
    public DataResult<PersonInfoResponse> identification(@RequestBody CaseRiskIdentificationRequest caseRiskIdentificationRequest) {
        return DataResult.ok(caseRiskIdentificationService.search(caseRiskIdentificationRequest.getSearchContent()));
    }

    @Operation(summary = "ai标签与总结")
    @PostMapping("/summary")
    public DataResult<PersonRiskResponse> summary(@RequestBody PersonTagsAndSummaryRequest request) {
        return DataResult.ok(caseRiskIdentificationService.generateTagsAndSummary(request.getIdNumber(), request.getName()));
    }

    @Operation(summary = "历史来访记录")
    @PostMapping("/historicalVisitRecords")
    public DataResult<HistoricalVisitRecords> historicalVisitRecords(@RequestBody HistoryPetitionRequest request) {
        return DataResult.ok(caseRiskIdentificationService.listHistoricalVisitRecord(request.getIdNumber(), request.getName()));
    }

    @Operation(summary = "类案推荐")
    @PostMapping("/recommendedCases")
    public DataResult<List<RecommendedCase>> recommendedCases(@RequestBody RecommendedCasesRequest request) {
        return DataResult.ok(caseRiskIdentificationService.listRecommendedCase(request.getBrief()));
    }

    @Operation(summary = "推荐案件详情")
    @GetMapping("/recommendedCaseDetail")
    public DataResult<RecommendedCaseDetail> getCaseDetail(String wenshuId) {
        return DataResult.ok(caseRiskIdentificationService.getRecommendedCaseDetail(wenshuId));
    }

    @Operation(summary = "历史记录查询")
    @GetMapping("/history")
    public DataResult<List<CaseRiskIdentificationHistory>> history() {
        return DataResult.ok(caseRiskIdentificationService.listHistory());
    }

    @Operation(summary = "下载风险评估报告")
    @GetMapping("/downloadReport")
    public void downloadTemplate(String name, String idNumber, HttpServletResponse httpServletResponse) {
        caseRiskIdentificationService.download(name, idNumber, httpServletResponse);
    }
}
