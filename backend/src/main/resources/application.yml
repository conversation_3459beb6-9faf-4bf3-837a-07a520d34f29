spring:
  application:
    name: <PERSON><PERSON><PERSON><PERSON>
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          username: root  # 替换为你的MySQL用户名
          password: smxz.mysql.smxz  # 替换为你的MySQL密码
          url: *************************************************************************************************
        tyyw:
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          username: SYSDBA
          password: smxz@6789#JKL
          url: *****************************************
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure
  ai:
    dynamic:
      primary: qwen
      model:
        qwen:
          api-key: sk-55cee47cfc8648108fcedac23e38556a
          base-url: https://dashscope.aliyuncs.com/compatible-mode
          chat:
            options:
              model: qwen3-32b
              maxTokens: 8192
              temperature: 0.7
        deepseek:
          api-key: ***********************************
          base-url: https://api.deepseek.com
          chat:
            options:
              model: deepseek-chat
              temperature: 0.7
        ollama-qwen:
          api-type: ollama
          base-url: http://**************:11434
          chat:
            options:
              model: Qwen3:14b

server:
  port: 8081

scheduler:
  sync:
    ajxx:
      expression: 0 0/10 * * * ? #每10秒执行一次
writ:
  source: SFTP
sftp:
  host: **************
  port: 22
  username: wsxx
  password: Aa12345678
  workDirectory: wsxx


logging:
  level:
    # RestTemplate 的 DEBUG 日志
    org.springframework.web.client.RestTemplate: DEBUG
    # RetryTemplate 的 DEBUG 日志
    org.springframework.retry.support.RetryTemplate: DEBUG

    # HTTP 请求的详细日志（包括请求头、请求体）
    org.apache.http.wire: DEBUG

    # Jackson 的序列化/反序列化日志
    com.fasterxml.jackson.databind: DEBUG

    # 如果需要，还可以添加以下日志级别
    org.springframework.http.converter.json: DEBUG
    org.apache.http.headers: DEBUG
    # 全局业务日志开启
    com.smxz: DEBUG
ragflow:
  api-key: ragflow-I4YTA2NjljNDRlMDExZjBhMDY3NzIyMj

# 阿申相关配置
ashen:
  model-name: Qwen3:14b
  database-id: 0d0eda16450311f0b176722285ebb39f

# 对接一律可知相关的接口
recommended-case:
  token: a12c62e53510577fa307abebb65d2458
  base-url: http://legalone-gz.megatechai.com/api
  search:
    url: /v2/caseSearch
  detail:
    url: /v2/caseDetail

minio:
  endpoint: http://**************:19000
  accessKey: root
  secretKey: smxz.minio.smxz
  bucketName: scbg
smxz:
  onlyoffice:
    document-server:
      url: http://**************:9999