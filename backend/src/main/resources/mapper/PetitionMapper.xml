<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smxz.dakongshen.mapper.PetitionMapper">
    <select id="listPetitionByPetitionerName" resultType="com.smxz.dakongshen.entity.Petition">
        SELECT p.*
        FROM petition p LEFT JOIN petition_petitioner pp on pp.petition_id = p.id
        WHERE pp.petitioner_id IN (SELECT pp2.id FROM petitioner_portrait pp2 WHERE pp2.petitioner_name = #{petitionerName})
    </select>
</mapper>