<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smxz.dakongshen.mapper.PetitionDataRawMapper">

    <!-- 按信访类型统计数量 -->
    <select id="countByPetitionMethod" resultType="com.smxz.dakongshen.dto.response.NameValueItem">
        WITH RECURSIVE split_petition_method AS (
            -- 第一步：初始行，取第一个分号前的子串
            SELECT id,
                   TRIM(SUBSTRING_INDEX(petition_method, ';', 1))                                   AS name,
                   -- 剩余部分
                   SUBSTRING(petition_method, char_length(SUBSTRING_INDEX(petition_method, ';', 1)) + 2) AS rest
            FROM petition_data_raw
            <where>
                <if test="startTime != null">
                    AND petition_time &gt;= #{startTime}
                </if>
                <if test="endTime != null">
                    AND petition_time &lt;= #{endTime}
                </if>
                AND petition_channel = '来访'
            </where>

            UNION ALL

            -- 递归部分：继续分割剩余部分
            SELECT id,
                   TRIM(SUBSTRING_INDEX(rest, ';', 1))                        AS name,
                   SUBSTRING(rest, char_length(SUBSTRING_INDEX(rest, ';', 1)) + 2) AS rest
            FROM split_petition_method
            WHERE rest IS NOT NULL
              AND rest != ''
            )
        SELECT name,
               COUNT(*) AS value
        FROM split_petition_method
        WHERE name IS NOT NULL
          AND name != ''
        GROUP BY name
        ORDER BY value DESC;
    </select>

    <!-- 按信访渠道统计数量 -->
    <select id="countByPetitionChannel" resultType="com.smxz.dakongshen.dto.response.NameValueItem">
        SELECT
            petition_channel as name,
            COUNT(*) as value
        FROM
            petition_data_raw
        <where>
            <if test="startTime != null">
                AND petition_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND petition_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY
            petition_channel
        ORDER BY
            value DESC
    </select>

    <!-- 按信访地区统计数量 -->
    <select id="countByPetitionLocation" resultType="com.smxz.dakongshen.dto.response.NameValueItem">
        SELECT
            organizer as name,
            COUNT(*) as value
        FROM
            petition_data_raw
        <where>
            <if test="startTime != null">
                AND petition_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND petition_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY
            organizer
        ORDER BY
            value DESC
    </select>

    <!-- 按涉案领域统计数量 -->
    <select id="countByInvolvementField" resultType="com.smxz.dakongshen.dto.response.NameValueItem">
        SELECT
            involvement_field as name,
            COUNT(*) as value
        FROM
            petition_data_raw
        <where>
            <if test="startTime != null">
                AND petition_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND petition_time &lt;= #{endTime}
            </if>
            and involvement_field is not null
        </where>

        GROUP BY
            involvement_field
        ORDER BY
            value DESC
    </select>

    <!-- 按时间段统计信访总量 -->
    <select id="countByTimeRange" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            petition_data_raw
        <where>
            <if test="startTime != null">
                AND petition_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND petition_time &lt;= #{endTime}
            </if>
        </where>

    </select>

    <!-- 按时间段统计同期信访总量 -->
    <select id="countDpByTimeRange" resultType="com.smxz.dakongshen.dto.response.AllSyCount">
        SELECT
            -- 当期全市数量
            SUM(CASE
                    WHEN petition_time >= #{startTime} AND petition_time &lt;= #{endTime}
        THEN 1 ELSE 0
        END) AS currentYearAllCount,

        -- 上期全市数量
        SUM(CASE
        WHEN petition_time >= (#{startTime} - INTERVAL 1 YEAR) AND petition_time &lt;= (#{endTime} - INTERVAL 1 YEAR)
        THEN 1 ELSE 0
        END) AS lastYearAllCount,

        -- 当期市院数量
        SUM(CASE
        WHEN petition_time >= #{startTime} AND petition_time &lt;= #{endTime} AND organizer = 'G市院'
        THEN 1 ELSE 0
        END) AS currentYearSyCount,

        -- 上期市院数量
        SUM(CASE
        WHEN petition_time >= (#{startTime} - INTERVAL 1 YEAR) AND petition_time &lt;= (#{endTime} - INTERVAL 1 YEAR) AND
        organizer = 'G市院'
        THEN 1 ELSE 0
        END) AS lastYearSyCount
        FROM
        petition_data_raw
        WHERE
        -- 扩展时间范围，包含当前和上一年的数据
        (petition_time >= (#{startTime} - INTERVAL 1 YEAR) AND petition_time &lt;= #{endTime})

    </select>
    
    <!-- 按月份统计信访量趋势 -->
    <select id="countByMonth" resultType="com.smxz.dakongshen.dto.response.MonthlyCountItem">
        SELECT
            DATE_FORMAT(petition_time, '%Y-%m') as month,
            COUNT(*) as count
        FROM
            petition_data_raw
        <where>
            <if test="startTime != null">
                AND petition_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND petition_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY
            DATE_FORMAT(petition_time, '%Y-%m')
        ORDER BY
            month ASC
    </select>

    
</mapper>