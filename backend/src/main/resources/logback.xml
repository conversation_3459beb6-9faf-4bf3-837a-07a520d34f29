<?xml version="1.0" encoding="utf-8"?>

<configuration debug="false">
    <define name="LOG_HOME" class="com.smxz.logback.LogbackHomeGetProperty" />
    <define name="IP" class="com.smxz.logback.LogbackIpGetProperty" />
    <define name="PORT" class="com.smxz.logback.LogbackPortGetProperty" />

    <property name="APP_NAME" value="dks" />

    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- -->
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <Target>System.out</Target>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss:SSS , GMT+8} %m [%c:%L]-[%p] %n</pattern>
        </encoder>
    </appender>

    <!-- -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}_stdout_${IP}_${PORT}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${APP_NAME}_stdout_${IP}_${PORT}.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <maxHistory>20</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5level] [%-5thread] %logger - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- -->
    <appender name="FILE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}_stderr_${IP}_${PORT}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>error</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG_HOME}/${APP_NAME}_stderr_${IP}_${PORT}.%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <maxHistory>20</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5level] [%-5thread] %logger{20} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- -->
    <appender name="FILE-JDBC" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${APP_NAME}_jdbc_${IP}_${PORT}.log</File>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%-5level] [%-5thread] %logger{20} - %msg%n</pattern>
        </encoder>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}_jdbc_${IP}_${PORT}.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
    </appender>



    <logger name="com.smxz" additivity="false">
        <appender-ref ref="stdout" />
        <level value="info" />
        <appender-ref ref="FILE" />
        <appender-ref ref="FILE-ERROR" />
    </logger>
    <logger name="jdbc.resultset" level="OFF"/>
    <logger name="jdbc.audit" level="OFF"/>
    <logger name="jdbc.connection" level="OFF"/>
    <logger name="jdbc.sqlonly" level="OFF"/>
    <logger name="jdbc.resultsettable" level="OFF"/>
    <logger name="jdbc.sqltiming" additivity="false">
        <appender-ref ref="FILE-JDBC" />
        <appender-ref ref="stdout" />

        <!--debug 控制输出方法名，appender中filter过滤warn级别以下的日志 -->
        <level value="info" />
    </logger>
    <root>
        <level value="warn" />
        <appender-ref ref="stdout" />
        <appender-ref ref="FILE" />
        <appender-ref ref="FILE-ERROR" />
    </root>
</configuration>